import { Router } from 'express';
import {
  getAdminStats,
  getUsers,
  updateUser,
  getOrders,
  getVouchers,
  bulkCreateVouchers,
} from '../controllers/adminController';
import {
  validateInput,
} from '../middleware/security';
import { authenticate, requireAdmin } from '../middleware/auth';
import { body, query, param } from 'express-validator';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(requireAdmin);

// User update validation
const updateUserValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid user ID format'),
  body('role')
    .optional()
    .isIn(['user', 'admin'])
    .withMessage('Role must be user or admin'),
  body('emailVerified')
    .optional()
    .isBoolean()
    .withMessage('Email verified must be boolean'),
  body('locked')
    .optional()
    .isBoolean()
    .withMessage('Locked must be boolean'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// User filtering validation
const userFilterValidation = [
  query('search')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Search term too long'),
  query('role')
    .optional()
    .isIn(['user', 'admin'])
    .withMessage('Invalid role filter'),
  query('verified')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Verified filter must be true or false'),
];

// Order filtering validation
const orderFilterValidation = [
  query('status')
    .optional()
    .isIn(['pending', 'payment_pending', 'paid', 'completed', 'cancelled', 'failed'])
    .withMessage('Invalid order status'),
  query('userId')
    .optional()
    .isUUID()
    .withMessage('Invalid user ID format'),
];

// Voucher filtering validation
const voucherFilterValidation = [
  query('status')
    .optional()
    .isIn(['active', 'redeemed', 'expired', 'cancelled'])
    .withMessage('Invalid voucher status'),
  query('userId')
    .optional()
    .isUUID()
    .withMessage('Invalid user ID format'),
];

// Bulk voucher creation validation
const bulkVoucherValidation = [
  body('count')
    .isInt({ min: 1, max: 1000 })
    .withMessage('Count must be between 1 and 1000'),
  body('expiryDays')
    .optional()
    .isInt({ min: 1, max: 3650 })
    .withMessage('Expiry days must be between 1 and 3650'),
];

/**
 * @route   GET /api/v1/admin/stats
 * @desc    Get admin dashboard statistics
 * @access  Private (Admin)
 */
router.get('/stats', getAdminStats);

/**
 * @route   GET /api/v1/admin/users
 * @desc    Get all users with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/users',
  validateInput([...paginationValidation, ...userFilterValidation]),
  getUsers
);

/**
 * @route   PUT /api/v1/admin/users/:id
 * @desc    Update user role or status
 * @access  Private (Admin)
 */
router.put(
  '/users/:id',
  validateInput(updateUserValidation),
  updateUser
);

/**
 * @route   GET /api/v1/admin/orders
 * @desc    Get all orders with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/orders',
  validateInput([...paginationValidation, ...orderFilterValidation]),
  getOrders
);

/**
 * @route   GET /api/v1/admin/vouchers
 * @desc    Get all vouchers with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/vouchers',
  validateInput([...paginationValidation, ...voucherFilterValidation]),
  getVouchers
);

/**
 * @route   POST /api/v1/admin/vouchers/bulk
 * @desc    Bulk create vouchers
 * @access  Private (Admin)
 */
router.post(
  '/vouchers/bulk',
  validateInput(bulkVoucherValidation),
  bulkCreateVouchers
);

export default router;
