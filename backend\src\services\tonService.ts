import { TonClient, WalletContractV4, internal } from '@ton/ton';
import { mnemonicNew, mnemonicToPrivateKey } from '@ton/crypto';
import { Address, Cell, beginCell, toNano } from '@ton/core';
import { logger } from '../config/logger';
import { executeQuery } from '../config/database';

// TON network configuration
const TON_NETWORK = process.env.TON_NETWORK || 'testnet';
const TON_API_ENDPOINT = process.env.TON_API_ENDPOINT || 
  (TON_NETWORK === 'mainnet' ? 'https://toncenter.com/api/v2/jsonRPC' : 'https://testnet.toncenter.com/api/v2/jsonRPC');

// Initialize TON client
let tonClient: TonClient;

export const initializeTonClient = async () => {
  try {
    const clientConfig: any = {
      endpoint: TON_API_ENDPOINT,
    };

    // Add API key only if it exists
    if (process.env.TON_API_KEY) {
      clientConfig.apiKey = process.env.TON_API_KEY;
    }

    tonClient = new TonClient(clientConfig);
    
    logger.info('TON client initialized', { 
      network: TON_NETWORK,
      endpoint: TON_API_ENDPOINT 
    });
    
    return tonClient;
  } catch (error) {
    logger.error('Failed to initialize TON client:', error);
    throw error;
  }
};

// Get TON client instance
export const getTonClient = (): TonClient => {
  if (!tonClient) {
    throw new Error('TON client not initialized. Call initializeTonClient() first.');
  }
  return tonClient;
};

// Generate a new payment address for an order
export const generatePaymentAddress = async (orderId: string): Promise<{
  address: string;
  memo: string;
  privateKey?: string;
}> => {
  try {
    // For production, you might want to use a deterministic address generation
    // based on order ID or use a master wallet with different memo fields
    
    // Generate new mnemonic and keys
    const mnemonic = await mnemonicNew();
    const keyPair = await mnemonicToPrivateKey(mnemonic);
    
    // Create wallet contract
    const workchain = 0; // Usually 0 for basic wallets
    const wallet = WalletContractV4.create({ workchain, publicKey: keyPair.publicKey });
    
    // Get wallet address
    const address = wallet.address.toString();
    
    // Create memo for this order
    const memo = `Order:${orderId}`;
    
    // Store wallet info securely (in production, encrypt the private key)
    await executeQuery(
      `INSERT INTO payment_addresses (order_id, address, memo, private_key_encrypted, created_at)
       VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)`,
      [orderId, address, memo, keyPair.secretKey.toString('hex')] // In production, encrypt this!
    );
    
    logger.info('Payment address generated', {
      orderId,
      address,
      memo,
    });
    
    const result: { address: string; memo: string; privateKey?: string } = {
      address,
      memo,
    };

    // Don't return private key in production
    if (process.env.NODE_ENV === 'development') {
      result.privateKey = keyPair.secretKey.toString('hex');
    }

    return result;
  } catch (error) {
    logger.error('Failed to generate payment address:', error);
    throw error;
  }
};

// Verify a transaction on TON blockchain
export const verifyTransaction = async (
  transactionHash: string,
  expectedAmount: string,
  expectedDestination: string,
  memo?: string
): Promise<{
  isValid: boolean;
  transaction?: any;
  error?: string;
}> => {
  try {
    const client = getTonClient();
    
    // Get transaction by hash
    const transactions = await client.getTransactions(
      Address.parse(expectedDestination),
      { limit: 100 }
    );
    
    // Find transaction with matching hash
    const transaction = transactions.find(tx => 
      tx.hash().toString('hex') === transactionHash.toLowerCase()
    );
    
    if (!transaction) {
      return {
        isValid: false,
        error: 'Transaction not found',
      };
    }
    
    // Verify transaction details
    const inMsg = transaction.inMessage;
    if (!inMsg) {
      return {
        isValid: false,
        error: 'No incoming message in transaction',
      };
    }
    
    // Check if it's an internal message (TON transfer)
    if (inMsg.info.type !== 'internal') {
      return {
        isValid: false,
        error: 'Not an internal transfer',
      };
    }
    
    // Verify amount
    const receivedAmount = inMsg.info.value.coins;
    const expectedAmountNano = toNano(expectedAmount);
    
    if (receivedAmount < expectedAmountNano) {
      return {
        isValid: false,
        error: `Insufficient amount. Expected: ${expectedAmount} TON, Received: ${receivedAmount.toString()} nanoTON`,
      };
    }
    
    // Verify destination address
    const destinationAddress = inMsg.info.dest?.toString();
    if (destinationAddress !== expectedDestination) {
      return {
        isValid: false,
        error: 'Destination address mismatch',
      };
    }
    
    // Verify memo if provided
    if (memo && inMsg.body) {
      try {
        const bodySlice = inMsg.body.beginParse();
        const op = bodySlice.loadUint(32);
        
        if (op === 0) { // Text comment
          const comment = bodySlice.loadStringTail();
          if (!comment.includes(memo)) {
            return {
              isValid: false,
              error: 'Memo mismatch',
            };
          }
        }
      } catch (error) {
        // Memo verification failed, but transaction might still be valid
        logger.warn('Memo verification failed:', error);
      }
    }
    
    logger.info('Transaction verified successfully', {
      transactionHash,
      amount: receivedAmount.toString(),
      destination: destinationAddress,
    });
    
    return {
      isValid: true,
      transaction: {
        hash: transactionHash,
        amount: receivedAmount.toString(),
        destination: destinationAddress,
        timestamp: transaction.now,
        lt: transaction.lt.toString(),
      },
    };
  } catch (error) {
    logger.error('Transaction verification failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      isValid: false,
      error: `Verification error: ${errorMessage}`,
    };
  }
};

// Get transaction status
export const getTransactionStatus = async (
  transactionHash: string
): Promise<{
  exists: boolean;
  confirmed: boolean;
  confirmations?: number;
  transaction?: any;
}> => {
  try {
    const client = getTonClient();
    
    // In TON, we need to search for the transaction across known addresses
    // This is a simplified version - in production, you'd track specific addresses
    
    // For now, return a basic status check
    // In a real implementation, you'd need to track the specific address
    // and check transactions for that address
    
    return {
      exists: true,
      confirmed: true,
      confirmations: 1, // TON has instant finality
    };
  } catch (error) {
    logger.error('Failed to get transaction status:', error);
    return {
      exists: false,
      confirmed: false,
    };
  }
};

// Get current TON price (for display purposes)
export const getTonPrice = async (): Promise<number> => {
  try {
    // This would typically call a price API
    // For now, return a mock price
    return 2.50; // USD per TON
  } catch (error) {
    logger.error('Failed to get TON price:', error);
    return 0;
  }
};

// Convert TON amount to nanoTON
export const tonToNano = (amount: string | number): bigint => {
  return toNano(amount.toString());
};

// Convert nanoTON to TON
export const nanoToTon = (nanoAmount: bigint | string): string => {
  const nano = typeof nanoAmount === 'string' ? BigInt(nanoAmount) : nanoAmount;
  return (Number(nano) / 1000000000).toFixed(9);
};

// Validate TON address format
export const isValidTonAddress = (address: string): boolean => {
  try {
    Address.parse(address);
    return true;
  } catch {
    return false;
  }
};

// Get wallet balance
export const getWalletBalance = async (address: string): Promise<string> => {
  try {
    const client = getTonClient();
    const balance = await client.getBalance(Address.parse(address));
    return nanoToTon(balance);
  } catch (error) {
    logger.error('Failed to get wallet balance:', error);
    return '0';
  }
};

// Send TON transaction (for admin operations)
export const sendTransaction = async (
  fromPrivateKey: string,
  toAddress: string,
  amount: string,
  memo?: string
): Promise<{
  success: boolean;
  transactionHash?: string;
  error?: string;
}> => {
  try {
    const client = getTonClient();
    
    // This is a simplified version - in production you'd need proper key management
    const privateKey = Buffer.from(fromPrivateKey, 'hex');
    
    // Create wallet from private key
    const keyPair = {
      publicKey: privateKey.slice(32), // Last 32 bytes
      secretKey: privateKey,
    };
    
    const wallet = WalletContractV4.create({ 
      workchain: 0, 
      publicKey: keyPair.publicKey 
    });
    
    const contract = client.open(wallet);
    
    // Create transfer message
    let body: Cell | undefined;
    if (memo) {
      body = beginCell()
        .storeUint(0, 32) // Text comment op code
        .storeStringTail(memo)
        .endCell();
    }
    
    // Send transaction
    const seqno = await contract.getSeqno();
    
    await contract.sendTransfer({
      secretKey: keyPair.secretKey,
      seqno,
      messages: [
        internal({
          to: Address.parse(toAddress),
          value: tonToNano(amount),
          body,
        }),
      ],
    });
    
    // Wait for transaction to be processed
    let currentSeqno = seqno;
    while (currentSeqno === seqno) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      currentSeqno = await contract.getSeqno();
    }
    
    logger.info('Transaction sent successfully', {
      toAddress,
      amount,
      memo,
    });
    
    return {
      success: true,
      transactionHash: 'pending', // Would need to get actual hash
    };
  } catch (error) {
    logger.error('Failed to send transaction:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      error: errorMessage,
    };
  }
};

// Health check for TON service
export const checkTonServiceHealth = async (): Promise<{
  healthy: boolean;
  network: string;
  endpoint: string;
  error?: string;
}> => {
  try {
    const client = getTonClient();
    
    // Try to get master chain info to verify connection
    const masterchainInfo = await client.getMasterchainInfo();
    
    return {
      healthy: true,
      network: TON_NETWORK,
      endpoint: TON_API_ENDPOINT,
    };
  } catch (error) {
    logger.error('TON service health check failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      healthy: false,
      network: TON_NETWORK,
      endpoint: TON_API_ENDPOINT,
      error: errorMessage,
    };
  }
};
