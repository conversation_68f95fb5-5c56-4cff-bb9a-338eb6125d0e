import { Router } from 'express';
import {
  initiatePayment,
  verifyPayment,
  getPaymentStatus,
  getPaymentHistory,
  cancelPayment,
} from '../controllers/paymentController';
import {
  validateInput,
} from '../middleware/security';
import { authenticate, requireUser, requireAdmin } from '../middleware/auth';
import { body, query, param } from 'express-validator';

const router = Router();

// Payment initiation validation
const initiatePaymentValidation = [
  body('orderId')
    .isUUID()
    .withMessage('Invalid order ID format'),
];

// Payment verification validation
const verifyPaymentValidation = [
  body('transactionHash')
    .matches(/^[a-fA-F0-9]{64}$/)
    .withMessage('Invalid transaction hash format'),
  body('orderId')
    .isUUID()
    .withMessage('Invalid order ID format'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Invalid amount'),
  body('fromAddress')
    .notEmpty()
    .withMessage('From address is required'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// UUID validation
const uuidValidation = [
  param('orderId')
    .isUUID()
    .withMessage('Invalid order ID format'),
];

/**
 * @route   POST /api/v1/payments/initiate
 * @desc    Initiate payment for an order
 * @access  Private (User)
 */
router.post(
  '/initiate',
  authenticate,
  requireUser,
  validateInput(initiatePaymentValidation),
  initiatePayment
);

/**
 * @route   POST /api/v1/payments/verify
 * @desc    Verify payment transaction
 * @access  Private (Admin) or Webhook
 */
router.post(
  '/verify',
  // Note: In production, this should be protected by webhook signature verification
  // For now, allowing admin access for manual verification
  authenticate,
  requireAdmin,
  validateInput(verifyPaymentValidation),
  verifyPayment
);

/**
 * @route   GET /api/v1/payments/:orderId/status
 * @desc    Get payment status for an order
 * @access  Private (User - own orders only)
 */
router.get(
  '/:orderId/status',
  authenticate,
  requireUser,
  validateInput(uuidValidation),
  getPaymentStatus
);

/**
 * @route   GET /api/v1/payments/history
 * @desc    Get payment history for user
 * @access  Private (User)
 */
router.get(
  '/history',
  authenticate,
  requireUser,
  validateInput(paginationValidation),
  getPaymentHistory
);

/**
 * @route   PUT /api/v1/payments/:orderId/cancel
 * @desc    Cancel payment for an order
 * @access  Private (User - own orders only)
 */
router.put(
  '/:orderId/cancel',
  authenticate,
  requireUser,
  validateInput(uuidValidation),
  cancelPayment
);

export default router;
