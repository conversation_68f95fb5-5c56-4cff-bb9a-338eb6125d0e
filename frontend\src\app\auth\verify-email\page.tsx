'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { CheckCircle, XCircle, Mail, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function VerifyEmailPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading');
  const [message, setMessage] = useState('');
  const [resendEmail, setResendEmail] = useState('');
  const [resending, setResending] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { verifyEmail, resendVerification } = useAuth();

  const token = searchParams.get('token');

  useEffect(() => {
    if (token) {
      handleVerification(token);
    } else {
      setStatus('error');
      setMessage('No verification token provided');
    }
  }, [token]);

  const handleVerification = async (verificationToken: string) => {
    try {
      const success = await verifyEmail(verificationToken);
      if (success) {
        setStatus('success');
        setMessage('Your email has been verified successfully!');
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth/login');
        }, 3000);
      } else {
        setStatus('error');
        setMessage('Verification failed. The token may be invalid or expired.');
      }
    } catch (error: any) {
      if (error.response?.data?.code === 'TOKEN_EXPIRED') {
        setStatus('expired');
        setMessage('The verification token has expired. Please request a new one.');
      } else {
        setStatus('error');
        setMessage('Verification failed. Please try again.');
      }
    }
  };

  const handleResendVerification = async () => {
    if (!resendEmail) {
      alert('Please enter your email address');
      return;
    }

    setResending(true);
    try {
      await resendVerification(resendEmail);
      setMessage('A new verification email has been sent to your inbox.');
    } catch (error) {
      setMessage('Failed to send verification email. Please try again.');
    } finally {
      setResending(false);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="text-center">
            <div className="spinner-lg mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Verifying your email...
            </h2>
            <p className="text-gray-600">
              Please wait while we verify your email address.
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <CheckCircle className="h-16 w-16 text-success-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Email Verified!
            </h2>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <p className="text-sm text-gray-500 mb-4">
              You will be redirected to the login page in a few seconds...
            </p>
            <Link
              href="/auth/login"
              className="btn-primary"
            >
              Continue to Login
            </Link>
          </div>
        );

      case 'error':
      case 'expired':
        return (
          <div className="text-center">
            <XCircle className="h-16 w-16 text-error-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Verification Failed
            </h2>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            
            {status === 'expired' && (
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Request New Verification Email
                </h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="email" className="form-label">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="email"
                        value={resendEmail}
                        onChange={(e) => setResendEmail(e.target.value)}
                        className="form-input pl-10"
                        placeholder="Enter your email address"
                      />
                    </div>
                  </div>
                  <button
                    onClick={handleResendVerification}
                    disabled={resending}
                    className="btn-primary w-full"
                  >
                    {resending ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="h-4 w-4 mr-2" />
                        Send New Verification Email
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}

            <div className="space-y-3">
              <Link
                href="/auth/register"
                className="btn-outline w-full"
              >
                Back to Registration
              </Link>
              <Link
                href="/auth/login"
                className="btn-ghost w-full"
              >
                Try Login Instead
              </Link>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gradient">TON Vouchers</h1>
        </div>

        {/* Content */}
        <div className="card">
          <div className="card-body">
            {renderContent()}
          </div>
        </div>

        {/* Help Text */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Need help?{' '}
            <Link
              href="/support"
              className="font-medium text-ton-600 hover:text-ton-500"
            >
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
