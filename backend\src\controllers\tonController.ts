import { Request, Response } from 'express';
import { 
  getWalletBalance, 
  isValidTonAddress, 
  getTonPrice,
  checkTonServiceHealth,
  getTransactionStatus,
  nanoToTon,
  tonToNano 
} from '../services/tonService';
import { logger } from '../config/logger';

// Get wallet balance
export const getBalance = async (req: Request, res: Response) => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!isValidTonAddress(address)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid TON address format',
      });
    }

    const balance = await getWalletBalance(address);

    res.json({
      success: true,
      data: {
        address,
        balance,
        balanceNano: tonToNano(balance).toString(),
      },
    });
  } catch (error) {
    logger.error('Get balance error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get wallet balance',
    });
  }
};

// Get current TON price
export const getPrice = async (req: Request, res: Response) => {
  try {
    const price = await getTonPrice();

    res.json({
      success: true,
      data: {
        price,
        currency: 'USD',
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Get price error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get TON price',
    });
  }
};

// Validate TON address
export const validateAddress = async (req: Request, res: Response) => {
  try {
    const { address } = req.body;

    if (!address) {
      return res.status(400).json({
        success: false,
        error: 'Address is required',
      });
    }

    const isValid = isValidTonAddress(address);

    res.json({
      success: true,
      data: {
        address,
        isValid,
      },
    });
  } catch (error) {
    logger.error('Validate address error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate address',
    });
  }
};

// Get transaction status
export const getTransactionInfo = async (req: Request, res: Response) => {
  try {
    const { hash } = req.params;

    if (!hash || hash.length !== 64) {
      return res.status(400).json({
        success: false,
        error: 'Invalid transaction hash format',
      });
    }

    const status = await getTransactionStatus(hash);

    res.json({
      success: true,
      data: {
        hash,
        ...status,
      },
    });
  } catch (error) {
    logger.error('Get transaction info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get transaction information',
    });
  }
};

// Convert amounts between TON and nanoTON
export const convertAmount = async (req: Request, res: Response) => {
  try {
    const { amount, from, to } = req.body;

    if (!amount || !from || !to) {
      return res.status(400).json({
        success: false,
        error: 'Amount, from, and to parameters are required',
      });
    }

    if (!['ton', 'nano'].includes(from) || !['ton', 'nano'].includes(to)) {
      return res.status(400).json({
        success: false,
        error: 'From and to must be either "ton" or "nano"',
      });
    }

    let result: string;

    if (from === 'ton' && to === 'nano') {
      result = tonToNano(amount).toString();
    } else if (from === 'nano' && to === 'ton') {
      result = nanoToTon(BigInt(amount));
    } else {
      result = amount.toString(); // Same unit
    }

    res.json({
      success: true,
      data: {
        originalAmount: amount,
        convertedAmount: result,
        from,
        to,
      },
    });
  } catch (error) {
    logger.error('Convert amount error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to convert amount',
    });
  }
};

// Health check for TON service
export const healthCheck = async (req: Request, res: Response) => {
  try {
    const health = await checkTonServiceHealth();

    res.status(health.healthy ? 200 : 503).json({
      success: health.healthy,
      data: health,
    });
  } catch (error) {
    logger.error('TON health check error:', error);
    res.status(503).json({
      success: false,
      error: 'TON service health check failed',
    });
  }
};

// Get network information
export const getNetworkInfo = async (req: Request, res: Response) => {
  try {
    const network = process.env.TON_NETWORK || 'testnet';
    const endpoint = process.env.TON_API_ENDPOINT;

    res.json({
      success: true,
      data: {
        network,
        endpoint,
        isMainnet: network === 'mainnet',
        explorerUrl: network === 'mainnet' 
          ? 'https://tonviewer.com' 
          : 'https://testnet.tonviewer.com',
      },
    });
  } catch (error) {
    logger.error('Get network info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get network information',
    });
  }
};
