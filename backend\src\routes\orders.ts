import { Router } from 'express';
import {
  createOrder,
  getOrderById,
  getUserOrders,
  cancelOrder,
  updateOrderStatus,
  getOrderStats,
} from '../controllers/orderController';
import {
  validateInput,
  amountValidation,
  memoValidation,
  orderRateLimit,
} from '../middleware/security';
import { authenticate, requireUser, requireAdmin } from '../middleware/auth';
import { body, query, param } from 'express-validator';

const router = Router();

// All order routes require authentication
router.use(authenticate);

// Order creation validation
const createOrderValidation = [
  amountValidation,
  body('currency')
    .optional()
    .isIn(['TON'])
    .withMessage('Currency must be TON'),
  memoValidation,
];

// Order status validation
const updateOrderStatusValidation = [
  body('status')
    .isIn(['pending', 'payment_pending', 'paid', 'completed', 'cancelled', 'failed'])
    .withMessage('Invalid order status'),
  body('transactionHash')
    .optional()
    .matches(/^[a-fA-F0-9]{64}$/)
    .withMessage('Invalid transaction hash format'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// Order status filter validation
const orderStatusValidation = [
  query('status')
    .optional()
    .isIn(['pending', 'payment_pending', 'paid', 'completed', 'cancelled', 'failed'])
    .withMessage('Invalid order status'),
];

// UUID validation
const uuidValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid order ID format'),
];

/**
 * @route   POST /api/v1/orders
 * @desc    Create a new order
 * @access  Private (User)
 */
router.post(
  '/',
  requireUser,
  orderRateLimit,
  validateInput(createOrderValidation),
  createOrder
);

/**
 * @route   GET /api/v1/orders
 * @desc    Get user orders with pagination and filtering
 * @access  Private (User)
 */
router.get(
  '/',
  requireUser,
  validateInput([...paginationValidation, ...orderStatusValidation]),
  getUserOrders
);

/**
 * @route   GET /api/v1/orders/stats
 * @desc    Get user order statistics
 * @access  Private (User)
 */
router.get(
  '/stats',
  requireUser,
  getOrderStats
);

/**
 * @route   GET /api/v1/orders/:id
 * @desc    Get order by ID
 * @access  Private (User - own orders only)
 */
router.get(
  '/:id',
  requireUser,
  validateInput(uuidValidation),
  getOrderById
);

/**
 * @route   PUT /api/v1/orders/:id/cancel
 * @desc    Cancel an order
 * @access  Private (User - own orders only)
 */
router.put(
  '/:id/cancel',
  requireUser,
  validateInput(uuidValidation),
  cancelOrder
);

/**
 * @route   PUT /api/v1/orders/:id/status
 * @desc    Update order status (admin only)
 * @access  Private (Admin)
 */
router.put(
  '/:id/status',
  requireAdmin,
  validateInput([...uuidValidation, ...updateOrderStatusValidation]),
  updateOrderStatus
);

export default router;
