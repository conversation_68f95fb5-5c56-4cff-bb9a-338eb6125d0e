'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { TonConnectUI, TonConnectUiOptions, THEME } from '@tonconnect/ui-react';
import { Sender, SenderArguments } from '@ton/core';
import { toast } from 'react-hot-toast';

// TON Connect configuration
const manifestUrl = process.env.NEXT_PUBLIC_TON_MANIFEST_URL || 'http://localhost:3000/tonconnect-manifest.json';

const tonConnectOptions: TonConnectUiOptions = {
  uiPreferences: {
    theme: THEME.LIGHT,
    borderRadius: 'm',
    colorsSet: {
      [THEME.LIGHT]: {
        connectButton: {
          background: '#0ea5e9',
          foreground: '#ffffff',
        },
        accent: '#0ea5e9',
        telegramButton: '#0ea5e9',
      },
    },
  },
};

// Wallet interface
interface WalletInfo {
  address: string;
  balance: string;
  network: string;
  publicKey?: string;
}

// TON Connect context interface
interface TonConnectContextType {
  tonConnectUI: TonConnectUI | null;
  wallet: WalletInfo | null;
  connected: boolean;
  connecting: boolean;
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  sendTransaction: (transaction: any) => Promise<string>;
  getBalance: () => Promise<string>;
  refreshWallet: () => Promise<void>;
}

// Create context
const TonConnectContext = createContext<TonConnectContextType | undefined>(undefined);

// TON Connect provider component
export const TonConnectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [tonConnectUI, setTonConnectUI] = useState<TonConnectUI | null>(null);
  const [wallet, setWallet] = useState<WalletInfo | null>(null);
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);

  // Initialize TON Connect UI
  useEffect(() => {
    const initTonConnect = async () => {
      try {
        // Wait for DOM to be ready
        if (typeof window === 'undefined') return;

        // Create a hidden container for TonConnect UI if it doesn't exist
        let container = document.getElementById('ton-connect-container');
        if (!container) {
          container = document.createElement('div');
          container.id = 'ton-connect-container';
          container.style.display = 'none';
          document.body.appendChild(container);
        }

        const ui = new TonConnectUI({
          ...tonConnectOptions,
          buttonRootId: 'ton-connect-container',
          manifestUrl: manifestUrl,
        });

        setTonConnectUI(ui);

        // Set up event listeners
        ui.onStatusChange((walletInfo) => {
          if (walletInfo) {
            setWallet({
              address: walletInfo.account.address,
              balance: '0', // Will be fetched separately
              network: walletInfo.account.chain === '-239' ? 'mainnet' : 'testnet',
              publicKey: walletInfo.account.publicKey || undefined,
            });
            setConnected(true);
            toast.success('Wallet connected successfully!');

            // Fetch balance
            fetchBalance(walletInfo.account.address);
          } else {
            setWallet(null);
            setConnected(false);
          }
          setConnecting(false);
        });

        // Check if already connected
        if (ui.wallet) {
          setWallet({
            address: ui.wallet.account.address,
            balance: '0',
            network: ui.wallet.account.chain === '-239' ? 'mainnet' : 'testnet',
            publicKey: ui.wallet.account.publicKey || undefined,
          });
          setConnected(true);
          fetchBalance(ui.wallet.account.address);
        }
      } catch (error) {
        console.error('Failed to initialize TON Connect:', error);
        toast.error('Failed to initialize wallet connection');
      }
    };

    // Delay initialization to ensure DOM is ready
    const timer = setTimeout(initTonConnect, 100);
    return () => clearTimeout(timer);
  }, []);

  // Fetch wallet balance
  const fetchBalance = async (address: string) => {
    try {
      const { tonApi } = await import('@/lib/api');
      const response = await tonApi.getBalance(address);
      if (response.success && response.data) {
        setWallet(prev => prev ? { ...prev, balance: response.data.balance } : null);
      }
    } catch (error) {
      console.error('Failed to fetch balance:', error);
    }
  };

  // Connect wallet
  const connect = async (): Promise<void> => {
    if (!tonConnectUI) {
      throw new Error('TON Connect UI not initialized');
    }

    try {
      setConnecting(true);
      await tonConnectUI.openModal();
    } catch (error) {
      setConnecting(false);
      console.error('Failed to connect wallet:', error);
      toast.error('Failed to connect wallet');
      throw error;
    }
  };

  // Disconnect wallet
  const disconnect = async (): Promise<void> => {
    if (!tonConnectUI) {
      throw new Error('TON Connect UI not initialized');
    }

    try {
      await tonConnectUI.disconnect();
      setWallet(null);
      setConnected(false);
      toast.success('Wallet disconnected');
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
      toast.error('Failed to disconnect wallet');
      throw error;
    }
  };

  // Send transaction
  const sendTransaction = async (transaction: any): Promise<string> => {
    if (!tonConnectUI || !connected) {
      throw new Error('Wallet not connected');
    }

    try {
      const result = await tonConnectUI.sendTransaction(transaction);
      toast.success('Transaction sent successfully!');
      return result.boc;
    } catch (error) {
      console.error('Failed to send transaction:', error);
      toast.error('Failed to send transaction');
      throw error;
    }
  };

  // Get current balance
  const getBalance = async (): Promise<string> => {
    if (!wallet) {
      throw new Error('Wallet not connected');
    }

    try {
      await fetchBalance(wallet.address);
      return wallet.balance;
    } catch (error) {
      console.error('Failed to get balance:', error);
      throw error;
    }
  };

  // Refresh wallet info
  const refreshWallet = async (): Promise<void> => {
    if (!wallet) {
      throw new Error('Wallet not connected');
    }

    try {
      await fetchBalance(wallet.address);
    } catch (error) {
      console.error('Failed to refresh wallet:', error);
      throw error;
    }
  };

  const value: TonConnectContextType = {
    tonConnectUI,
    wallet,
    connected,
    connecting,
    connect,
    disconnect,
    sendTransaction,
    getBalance,
    refreshWallet,
  };

  return (
    <TonConnectContext.Provider value={value}>
      {children}
    </TonConnectContext.Provider>
  );
};

// Hook to use TON Connect context
export const useTonConnect = (): TonConnectContextType => {
  const context = useContext(TonConnectContext);
  if (context === undefined) {
    throw new Error('useTonConnect must be used within a TonConnectProvider');
  }
  return context;
};

// Custom sender for TON transactions
export class TonConnectSender implements Sender {
  private tonConnectUI: TonConnectUI;

  constructor(tonConnectUI: TonConnectUI) {
    this.tonConnectUI = tonConnectUI;
  }

  async send(args: SenderArguments): Promise<void> {
    const transaction = {
      validUntil: Math.floor(Date.now() / 1000) + 300, // 5 minutes
      messages: [
        {
          address: args.to.toString(),
          amount: args.value.toString(),
          payload: args.body?.toBoc().toString('base64') || undefined,
        },
      ],
    };

    await this.tonConnectUI.sendTransaction(transaction);
  }
}

export default TonConnectContext;
