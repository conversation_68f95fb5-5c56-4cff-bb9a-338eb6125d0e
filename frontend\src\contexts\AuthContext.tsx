'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authApi, clearTokens, getTokens } from '@/lib/api';
import { toast } from 'react-hot-toast';

// User interface
interface User {
  id: string;
  email: string;
  telegramId: string;
  emailVerified: boolean;
  role: 'user' | 'admin';
  createdAt: string;
  updatedAt: string;
}

// Auth context interface
interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  verifyEmail: (token: string) => Promise<boolean>;
  resendVerification: (email: string) => Promise<boolean>;
  requestPasswordReset: (email: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string) => Promise<boolean>;
  refreshUser: () => Promise<void>;
}

// Registration data interface
interface RegisterData {
  email: string;
  telegramId: string;
  password: string;
  memo?: string;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if user is authenticated
  const isAuthenticated = !!user;

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const tokens = getTokens();
      
      if (tokens.accessToken) {
        // Try to get user info
        const response = await authApi.getMe();
        if (response.success && response.data?.user) {
          setUser(response.data.user);
        } else {
          // Invalid token, clear it
          clearTokens();
        }
      }
    } catch (error) {
      // Token is invalid, clear it
      clearTokens();
    } finally {
      setLoading(false);
    }
  };

  // Login function
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authApi.login({ email, password });
      
      if (response.success && response.data?.user) {
        setUser(response.data.user);
        toast.success('Login successful!');
        return true;
      } else {
        toast.error(response.error || 'Login failed');
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Login failed';
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (data: RegisterData): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authApi.register(data);
      
      if (response.success) {
        toast.success('Registration successful! Please check your email to verify your account.');
        return true;
      } else {
        toast.error(response.error || 'Registration failed');
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Registration failed';
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout();
    } catch (error) {
      // Even if logout fails on server, clear local state
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      clearTokens();
      toast.success('Logged out successfully');
    }
  };

  // Verify email function
  const verifyEmail = async (token: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authApi.verifyEmail(token);
      
      if (response.success) {
        toast.success('Email verified successfully!');
        // Refresh user data if logged in
        if (user) {
          await refreshUser();
        }
        return true;
      } else {
        toast.error(response.error || 'Email verification failed');
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Email verification failed';
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Resend verification email
  const resendVerification = async (email: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authApi.resendVerification(email);
      
      if (response.success) {
        toast.success('Verification email sent!');
        return true;
      } else {
        toast.error(response.error || 'Failed to send verification email');
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to send verification email';
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Request password reset
  const requestPasswordReset = async (email: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authApi.requestPasswordReset(email);
      
      if (response.success) {
        toast.success('Password reset email sent!');
        return true;
      } else {
        toast.error(response.error || 'Failed to send password reset email');
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to send password reset email';
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (token: string, newPassword: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authApi.resetPassword({ token, newPassword });
      
      if (response.success) {
        toast.success('Password reset successfully!');
        return true;
      } else {
        toast.error(response.error || 'Password reset failed');
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Password reset failed';
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Refresh user data
  const refreshUser = async (): Promise<void> => {
    try {
      const response = await authApi.getMe();
      if (response.success && response.data?.user) {
        setUser(response.data.user);
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    verifyEmail,
    resendVerification,
    requestPasswordReset,
    resetPassword,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
