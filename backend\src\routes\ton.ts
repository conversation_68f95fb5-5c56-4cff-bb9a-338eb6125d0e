import { Router } from 'express';
import {
  getBalance,
  getPrice,
  validateAddress,
  getTransactionInfo,
  convertAmount,
  healthCheck,
  getNetworkInfo,
} from '../controllers/tonController';
import {
  validateInput,
} from '../middleware/security';
import { body, param } from 'express-validator';

const router = Router();

// Address validation
const addressValidation = [
  param('address')
    .notEmpty()
    .withMessage('Address is required')
    .isLength({ min: 48, max: 48 })
    .withMessage('Invalid address length'),
];

// Transaction hash validation
const transactionHashValidation = [
  param('hash')
    .matches(/^[a-fA-F0-9]{64}$/)
    .withMessage('Invalid transaction hash format'),
];

// Address validation for POST requests
const validateAddressValidation = [
  body('address')
    .notEmpty()
    .withMessage('Address is required'),
];

// Amount conversion validation
const convertAmountValidation = [
  body('amount')
    .notEmpty()
    .withMessage('Amount is required'),
  body('from')
    .isIn(['ton', 'nano'])
    .withMessage('From must be either "ton" or "nano"'),
  body('to')
    .isIn(['ton', 'nano'])
    .withMessage('To must be either "ton" or "nano"'),
];

/**
 * @route   GET /api/v1/ton/balance/:address
 * @desc    Get wallet balance for TON address
 * @access  Public
 */
router.get(
  '/balance/:address',
  validateInput(addressValidation),
  getBalance
);

/**
 * @route   GET /api/v1/ton/price
 * @desc    Get current TON price in USD
 * @access  Public
 */
router.get('/price', getPrice);

/**
 * @route   POST /api/v1/ton/validate-address
 * @desc    Validate TON address format
 * @access  Public
 */
router.post(
  '/validate-address',
  validateInput(validateAddressValidation),
  validateAddress
);

/**
 * @route   GET /api/v1/ton/transaction/:hash
 * @desc    Get transaction information by hash
 * @access  Public
 */
router.get(
  '/transaction/:hash',
  validateInput(transactionHashValidation),
  getTransactionInfo
);

/**
 * @route   POST /api/v1/ton/convert
 * @desc    Convert between TON and nanoTON
 * @access  Public
 */
router.post(
  '/convert',
  validateInput(convertAmountValidation),
  convertAmount
);

/**
 * @route   GET /api/v1/ton/health
 * @desc    Check TON service health
 * @access  Public
 */
router.get('/health', healthCheck);

/**
 * @route   GET /api/v1/ton/network
 * @desc    Get network information
 * @access  Public
 */
router.get('/network', getNetworkInfo);

export default router;
