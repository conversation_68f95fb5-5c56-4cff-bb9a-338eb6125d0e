import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logPaymentEvent } from '../config/logger';
import { generateVoucher } from './voucherController';
import { verifyTransaction } from '../services/tonService';

// Initiate payment for an order
export const initiatePayment = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { orderId } = req.body;

    // Get order details
    const orderResult = await executeQuery(
      'SELECT id, user_id, status, amount, currency, payment_address, payment_expires_at FROM orders WHERE id = $1',
      [orderId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    // Check if user owns this order
    if (order.user_id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'You do not own this order',
      });
    }

    // Check order status
    if (order.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: `Order is ${order.status} and cannot be paid`,
      });
    }

    // Check if payment has expired
    if (new Date() > new Date(order.payment_expires_at)) {
      // Update order status to failed
      await executeQuery(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['failed', orderId]
      );

      return res.status(400).json({
        success: false,
        error: 'Payment window has expired',
      });
    }

    // Update order status to payment_pending
    await executeQuery(
      'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      ['payment_pending', orderId]
    );

    logPaymentEvent('PAYMENT_INITIATED', orderId, order.amount, {
      userId,
      paymentAddress: order.payment_address,
    });

    res.json({
      success: true,
      message: 'Payment initiated successfully',
      data: {
        order: {
          id: order.id,
          amount: order.amount,
          currency: order.currency,
          paymentAddress: order.payment_address,
          expiresAt: order.payment_expires_at,
        },
        paymentInstructions: {
          message: 'Send the exact amount to the payment address',
          amount: `${order.amount} ${order.currency}`,
          address: order.payment_address,
          network: 'TON',
          memo: `Order: ${order.id}`,
        },
      },
    });
  } catch (error) {
    logger.error('Initiate payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate payment',
    });
  }
};

// Verify payment (webhook or manual verification)
export const verifyPayment = async (req: Request, res: Response) => {
  try {
    const { transactionHash, orderId, amount, fromAddress } = req.body;

    // Get order details
    const orderResult = await executeQuery(
      'SELECT id, user_id, status, amount as order_amount, currency, payment_address FROM orders WHERE id = $1',
      [orderId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    // Check if order can be paid
    if (!['payment_pending', 'pending'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        error: `Order is ${order.status} and cannot be paid`,
      });
    }

    // Validate amount (allow small tolerance for fees)
    const orderAmount = parseFloat(order.order_amount);
    const paidAmount = parseFloat(amount);
    const tolerance = 0.001; // 0.001 TON tolerance

    if (Math.abs(orderAmount - paidAmount) > tolerance) {
      return res.status(400).json({
        success: false,
        error: 'Payment amount does not match order amount',
      });
    }

    // Check if transaction already exists
    const existingTransaction = await executeQuery(
      'SELECT id FROM transactions WHERE hash = $1',
      [transactionHash]
    );

    if (existingTransaction.rows.length > 0) {
      return res.status(409).json({
        success: false,
        error: 'Transaction already processed',
      });
    }

    // Verify transaction on TON blockchain
    const verificationResult = await verifyTransaction(
      transactionHash,
      amount,
      order.payment_address,
      `Order:${orderId}`
    );

    const isValidTransaction = verificationResult.isValid;

    if (!isValidTransaction) {
      return res.status(400).json({
        success: false,
        error: verificationResult.error || 'Transaction verification failed',
      });
    }

    // Process payment in transaction
    const queries = [
      {
        text: `UPDATE orders 
               SET status = $1, transaction_hash = $2, updated_at = CURRENT_TIMESTAMP 
               WHERE id = $3`,
        params: ['paid', transactionHash, orderId],
      },
      {
        text: `INSERT INTO transactions (id, order_id, hash, from_address, to_address, amount, status, confirmations)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        params: [
          uuidv4(),
          orderId,
          transactionHash,
          fromAddress,
          order.payment_address,
          amount,
          'confirmed',
          1,
        ],
      },
    ];

    await executeTransaction(queries);

    // Generate voucher for the order
    try {
      await generateVoucher(orderId, order.user_id);
      
      // Update order status to completed
      await executeQuery(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['completed', orderId]
      );
    } catch (voucherError) {
      logger.error('Failed to generate voucher after payment:', voucherError);
      // Keep order as paid, voucher can be generated manually
    }

    logPaymentEvent('PAYMENT_VERIFIED', orderId, amount, {
      userId: order.user_id,
      transactionHash,
      fromAddress,
    });

    res.json({
      success: true,
      message: 'Payment verified and processed successfully',
      data: {
        transactionHash,
        orderId,
        amount,
        status: 'confirmed',
      },
    });
  } catch (error) {
    logger.error('Verify payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify payment',
    });
  }
};

// Get payment status
export const getPaymentStatus = async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const userId = req.user!.id;

    const paymentResult = await executeQuery(
      `SELECT o.id, o.status, o.amount, o.currency, o.payment_address, 
              o.payment_expires_at, o.transaction_hash, o.created_at, o.updated_at,
              t.hash as tx_hash, t.status as tx_status, t.confirmations
       FROM orders o
       LEFT JOIN transactions t ON o.id = t.order_id
       WHERE o.id = $1 AND o.user_id = $2`,
      [orderId, userId]
    );

    if (paymentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const payment = paymentResult.rows[0];

    res.json({
      success: true,
      data: {
        order: {
          id: payment.id,
          status: payment.status,
          amount: payment.amount,
          currency: payment.currency,
          paymentAddress: payment.payment_address,
          paymentExpiresAt: payment.payment_expires_at,
          transactionHash: payment.transaction_hash,
          createdAt: payment.created_at,
          updatedAt: payment.updated_at,
        },
        transaction: payment.tx_hash ? {
          hash: payment.tx_hash,
          status: payment.tx_status,
          confirmations: payment.confirmations,
        } : null,
      },
    });
  } catch (error) {
    logger.error('Get payment status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get payment status',
    });
  }
};

// Get payment history
export const getPaymentHistory = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await executeQuery(
      `SELECT COUNT(*) 
       FROM transactions t
       JOIN orders o ON t.order_id = o.id
       WHERE o.user_id = $1`,
      [userId]
    );
    const total = parseInt(countResult.rows[0].count);

    // Get transactions
    const transactionsResult = await executeQuery(
      `SELECT t.id, t.hash, t.from_address, t.to_address, t.amount, t.fee,
              t.status, t.confirmations, t.created_at, o.id as order_id
       FROM transactions t
       JOIN orders o ON t.order_id = o.id
       WHERE o.user_id = $1
       ORDER BY t.created_at DESC
       LIMIT $2 OFFSET $3`,
      [userId, limit, offset]
    );

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        transactions: transactionsResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get payment history',
    });
  }
};



// Cancel payment (if order is still pending)
export const cancelPayment = async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const userId = req.user!.id;

    // Get order
    const orderResult = await executeQuery(
      'SELECT id, user_id, status, amount FROM orders WHERE id = $1',
      [orderId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    // Check ownership
    if (order.user_id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'You do not own this order',
      });
    }

    // Check if payment can be cancelled
    if (!['pending', 'payment_pending'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        error: 'Payment cannot be cancelled',
      });
    }

    // Cancel the order
    await executeQuery(
      'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      ['cancelled', orderId]
    );

    logPaymentEvent('PAYMENT_CANCELLED', orderId, order.amount, { userId });

    res.json({
      success: true,
      message: 'Payment cancelled successfully',
    });
  } catch (error) {
    logger.error('Cancel payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel payment',
    });
  }
};
