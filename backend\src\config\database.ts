import { Pool, PoolConfig } from 'pg';
import { createClient, RedisClientType } from 'redis';
import { logger } from './logger';

// PostgreSQL Configuration with environment-aware SSL
const dbConfig: PoolConfig = {
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  database: process.env.DATABASE_NAME || 'tonsite',
  user: process.env.DATABASE_USER || 'postgres',
  password: process.env.DATABASE_PASSWORD || '',
  // Environment-aware SSL configuration
  ssl: process.env.NODE_ENV === 'production'
    ? { rejectUnauthorized: true }  // Production: Strict SSL
    : process.env.DATABASE_SSL === 'true'
      ? { rejectUnauthorized: false }  // Dev: Optional SSL for testing
      : false,  // Local: No SSL overhead
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  maxUses: 7500, // Close (and replace) a connection after it has been used 7500 times
};

// Create PostgreSQL connection pool
export const db = new Pool(dbConfig);

// PostgreSQL connection event handlers
db.on('connect', () => {
  logger.info('Connected to PostgreSQL database');
});

db.on('error', (err) => {
  logger.error('PostgreSQL connection error:', err);
});

// Redis Configuration with environment-based authentication
const redisConfig: any = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
  },
  database: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
};

// Add password only if it exists
if (process.env.REDIS_PASSWORD) {
  redisConfig.password = process.env.REDIS_PASSWORD;
}

// Create Redis client
export const redis: RedisClientType = createClient(redisConfig);

// Redis connection event handlers
redis.on('connect', () => {
  logger.info('Connected to Redis server');
});

redis.on('error', (err) => {
  logger.error('Redis connection error:', err);
});

redis.on('ready', () => {
  logger.info('Redis client ready');
});

redis.on('reconnecting', () => {
  logger.warn('Redis client reconnecting...');
});

// Database connection functions
export const connectDatabases = async (): Promise<void> => {
  try {
    // Test PostgreSQL connection
    const client = await db.connect();
    await client.query('SELECT NOW()');
    client.release();
    logger.info('PostgreSQL connection test successful');

    // Connect to Redis
    await redis.connect();
    logger.info('Redis connection successful');
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

export const disconnectDatabases = async (): Promise<void> => {
  try {
    await db.end();
    await redis.quit();
    logger.info('Database connections closed');
  } catch (error) {
    logger.error('Error closing database connections:', error);
    throw error;
  }
};

// Database health check
export const checkDatabaseHealth = async (): Promise<{ postgres: boolean; redis: boolean }> => {
  const health = { postgres: false, redis: false };

  try {
    // Check PostgreSQL
    const client = await db.connect();
    await client.query('SELECT 1');
    client.release();
    health.postgres = true;
  } catch (error) {
    logger.error('PostgreSQL health check failed:', error);
  }

  try {
    // Check Redis
    await redis.ping();
    health.redis = true;
  } catch (error) {
    logger.error('Redis health check failed:', error);
  }

  return health;
};

// Utility functions for database operations
export const executeQuery = async (text: string, params?: any[]): Promise<any> => {
  const client = await db.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    logger.error('Database query error:', { query: text, params, error });
    throw error;
  } finally {
    client.release();
  }
};

export const executeTransaction = async (queries: Array<{ text: string; params?: any[] }>): Promise<any[]> => {
  const client = await db.connect();
  try {
    await client.query('BEGIN');
    const results = [];
    
    for (const query of queries) {
      const result = await client.query(query.text, query.params);
      results.push(result);
    }
    
    await client.query('COMMIT');
    return results;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Transaction error:', error);
    throw error;
  } finally {
    client.release();
  }
};

// Redis utility functions
export const setCache = async (key: string, value: any, ttl?: number): Promise<void> => {
  try {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      await redis.setEx(key, ttl, serializedValue);
    } else {
      await redis.set(key, serializedValue);
    }
  } catch (error) {
    logger.error('Redis set error:', { key, error });
    throw error;
  }
};

export const getCache = async (key: string): Promise<any> => {
  try {
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error('Redis get error:', { key, error });
    return null;
  }
};

export const deleteCache = async (key: string): Promise<void> => {
  try {
    await redis.del(key);
  } catch (error) {
    logger.error('Redis delete error:', { key, error });
    throw error;
  }
};

export const setCacheWithPattern = async (pattern: string, value: any, ttl?: number): Promise<void> => {
  try {
    const keys = await redis.keys(pattern);
    const pipeline = redis.multi();
    
    keys.forEach(key => {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        pipeline.setEx(key, ttl, serializedValue);
      } else {
        pipeline.set(key, serializedValue);
      }
    });
    
    await pipeline.exec();
  } catch (error) {
    logger.error('Redis pattern set error:', { pattern, error });
    throw error;
  }
};

export const deleteCachePattern = async (pattern: string): Promise<void> => {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(keys);
    }
  } catch (error) {
    logger.error('Redis pattern delete error:', { pattern, error });
    throw error;
  }
};
