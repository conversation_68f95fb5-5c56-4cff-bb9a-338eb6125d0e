Authentication Setup Guide

## Overview

This project now implements **production-ready authentication with development-friendly defaults**. The system is designed to be:

- ✅ **Secure by default** in production
- ✅ **Developer-friendly** in local development
- ✅ **Environment-aware** with automatic configuration
- ✅ **Zero-hardcoded credentials** for security

## Quick Start (Development)

### 1. Environment Setup

```bash
# Copy the development environment template
cp backend/.env.example backend/.env

# The defaults are already development-friendly:
# - Simple Redis password: dev-redis-123
# - Development admin: <EMAIL> / DevAdmin123!
# - No SSL required for local development
```

### 2. Database Setup

```bash
# Start databases with authentication
docker run --name postgres-tonsite -e POSTGRES_PASSWORD=password -e POSTGRES_DB=tonsite_dev -p 5432:5432 -d postgres:14
docker run --name redis-tonsite --requirepass dev-redis-123 -p 6379:6379 -d redis:7

# Run schema (without hardcoded admin - now secure!)
docker exec -i postgres-tonsite psql -U postgres -d tonsite_dev < database/schema.sql
```

### 3. Start Development Server

```bash
cd backend
npm install
npm run dev

# The server will automatically:
# ✅ Validate environment configuration
# ✅ Connect to authenticated Redis
# ✅ Create admin user from environment variables
# ✅ Start with development-friendly settings
```

### 4. Admin Access

```bash
# Default development admin credentials:
Email: <EMAIL>
Password: DevAdmin123!
Telegram ID: admin_dev

# Or create custom admin:
ADMIN_EMAIL=<EMAIL> ADMIN_PASSWORD=YourPassword123! npm run setup-admin
```

## Security Improvements

### ❌ **BEFORE (Security Vulnerabilities)**

```sql
-- Hardcoded admin password in schema.sql
INSERT INTO users VALUES ('<EMAIL>', 'admin', '$2b$12$...', 'admin');
```

```conf
# Redis without authentication
# requirepass your-redis-password-here
```

### ✅ **AFTER (Secure & Dev-Friendly)**

```typescript
// Environment-based admin creation
const config = {
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'DevAdmin123!',
  // Production validation ensures strong passwords
};
```

```conf
# Environment-based Redis authentication
requirepass ${REDIS_PASSWORD:-dev-redis-123}
```

## Environment Configuration

### Development (.env)
```bash
# Development-friendly defaults
NODE_ENV=development
REDIS_PASSWORD=dev-redis-123
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=DevAdmin123!
DATABASE_SSL=false
```

### Production (.env.production)
```bash
# Production security enforced
NODE_ENV=production
REDIS_PASSWORD=SECURE_REDIS_PASSWORD_HERE
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=GENERATE_SECURE_PASSWORD_HERE
DATABASE_SSL=true
```

## Authentication Features

### 🔐 **JWT Token Management**
- Access tokens (15 minutes)
- Refresh tokens (7 days)
- Token blacklisting on logout
- Automatic token validation

### 🛡️ **Account Security**
- Password hashing with bcrypt
- Account lockout after failed attempts
- Session management and tracking
- IP and user agent logging

### 🚫 **Rate Limiting**
- Authentication endpoints protected
- Configurable limits per environment
- Brute force protection
- Suspicious activity detection

### 👤 **Role-Based Access**
- User and Admin roles
- Route-level authorization
- Permission checking middleware
- Admin activity logging

## Development Workflow

### Testing Authentication

```bash
# Register a new user
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","telegramId":"testuser","password":"Test123!"}'

# Login
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'

# Access protected route
curl -X GET http://localhost:3001/api/v1/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Admin Operations

```bash
# Setup admin manually
npm run setup-admin

# Setup admin for production
NODE_ENV=production npm run setup-admin:prod

# Check admin exists
psql -d tonsite_dev -c "SELECT email, role FROM users WHERE role = 'admin';"
```

## Production Deployment

### 1. Environment Validation

The system automatically validates production readiness:

```typescript
// Automatic checks in production:
✅ Strong JWT secrets (32+ characters)
✅ Secure admin password (12+ characters, complexity)
✅ Redis authentication enabled
✅ Database SSL enforced
✅ No default/weak passwords
```

### 2. Deployment Checklist

```bash
# 1. Set production environment variables
export NODE_ENV=production
export REDIS_PASSWORD="$(openssl rand -base64 32)"
export JWT_SECRET="$(openssl rand -base64 64)"
export ADMIN_PASSWORD="$(openssl rand -base64 24)"

# 2. Validate configuration
npm run build
node dist/index.js  # Will fail if not production-ready

# 3. Setup admin user
npm run setup-admin:prod

# 4. Start production server
npm start
```

### 3. Security Monitoring

```bash
# Check authentication logs
tail -f logs/app.log | grep "AUTH"

# Monitor failed login attempts
psql -d tonsite_prod -c "SELECT email, failed_login_attempts, locked_until FROM users WHERE failed_login_attempts > 0;"

# Review admin activity
psql -d tonsite_prod -c "SELECT * FROM admin_logs ORDER BY created_at DESC LIMIT 10;"
```

## Troubleshooting

### Common Development Issues

**Redis Connection Failed**
```bash
# Check Redis is running with password
docker exec redis-tonsite redis-cli -a dev-redis-123 ping
# Should return: PONG
```

**Admin Login Failed**
```bash
# Reset admin password
ADMIN_SETUP_REQUIRED=true npm run setup-admin
```

**Environment Validation Failed**
```bash
# Check required variables
node -e "require('./dist/config/env-validation').validateEnvironment()"
```

### Production Issues

**Production Readiness Check Failed**
```bash
# Check what's missing
node -e "console.log(require('./dist/config/env-validation').checkProductionReadiness())"
```

**Database SSL Connection Failed**
```bash
# Verify SSL certificate
openssl s_client -connect your-db-host:5432 -starttls postgres
```

## Migration from Old System

If upgrading from the previous hardcoded system:

```bash
# 1. Remove old hardcoded admin
psql -d tonsite -c "DELETE FROM users WHERE email = '<EMAIL>';"

# 2. Update environment variables
cp backend/.env.example backend/.env
# Edit .env with your preferences

# 3. Setup new secure admin
npm run setup-admin

# 4. Update Redis configuration
# Edit database/redis.conf to use environment-based password

# 5. Restart services
docker restart redis-tonsite
npm run dev
```

## Security Best Practices

### Development
- ✅ Use provided development defaults
- ✅ Never commit real credentials to git
- ✅ Test authentication flows regularly
- ✅ Use different passwords for each developer

### Production
- ✅ Generate strong, unique passwords
- ✅ Use environment variables for all secrets
- ✅ Enable SSL/TLS for all connections
- ✅ Monitor authentication logs
- ✅ Rotate credentials regularly
- ✅ Use secret management systems (AWS Secrets Manager, etc.)

## Support

For issues or questions:
1. Check the logs: `tail -f backend/logs/app.log`
2. Validate environment: `npm run setup-admin -- --validate`
3. Review this guide for common solutions
4. Check the audit log for security recommendations
