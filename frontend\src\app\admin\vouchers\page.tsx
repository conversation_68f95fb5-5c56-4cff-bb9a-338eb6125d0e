'use client';

import { useState, useEffect } from 'react';
import { adminApi } from '@/lib/api';
import { 
  Plus,
  Copy,
  Calendar,
  User,
  CheckCircle,
  XCircle,
  Clock,
  Gift
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Voucher {
  id: string;
  code: string;
  status: string;
  redeemed_at: string | null;
  expires_at: string;
  created_at: string;
  order_id: string;
  user_id: string;
  amount: string;
  currency: string;
  email: string;
  telegram_id: string;
}

export default function AdminVouchersPage() {
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('');
  const [userIdFilter, setUserIdFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showBulkModal, setShowBulkModal] = useState(false);

  useEffect(() => {
    fetchVouchers();
  }, [currentPage, statusFilter, userIdFilter]);

  const fetchVouchers = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 20,
      };

      if (statusFilter) params.status = statusFilter;
      if (userIdFilter) params.userId = userIdFilter;

      const response = await adminApi.getVouchers(params);
      
      if (response.success && response.data) {
        setVouchers(response.data.vouchers);
        setTotalPages(response.data.pagination.totalPages);
      }
    } catch (error) {
      console.error('Failed to fetch vouchers:', error);
      toast.error('Failed to load vouchers');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkCreate = async (count: number, expiryDays: number) => {
    try {
      const response = await adminApi.bulkCreateVouchers({ count, expiryDays });
      
      if (response.success) {
        toast.success(`${count} vouchers created successfully`);
        fetchVouchers();
        setShowBulkModal(false);
      } else {
        toast.error(response.error || 'Failed to create vouchers');
      }
    } catch (error) {
      console.error('Failed to create vouchers:', error);
      toast.error('Failed to create vouchers');
    }
  };

  const copyVoucherCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Voucher code copied to clipboard');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-success-500" />;
      case 'redeemed':
        return <Gift className="h-4 w-4 text-blue-500" />;
      case 'expired':
        return <Clock className="h-4 w-4 text-warning-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-error-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-success-600 bg-success-50 border-success-200';
      case 'redeemed':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'expired':
        return 'text-warning-600 bg-warning-50 border-warning-200';
      case 'cancelled':
        return 'text-error-600 bg-error-50 border-error-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vouchers</h1>
          <p className="text-gray-600">Manage and monitor all vouchers</p>
        </div>
        <button
          onClick={() => setShowBulkModal(true)}
          className="btn-primary inline-flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Bulk Create
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input-field"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="redeemed">Redeemed</option>
              <option value="expired">Expired</option>
              <option value="cancelled">Cancelled</option>
            </select>
            
            <input
              type="text"
              placeholder="Filter by User ID..."
              value={userIdFilter}
              onChange={(e) => setUserIdFilter(e.target.value)}
              className="input-field"
            />
            
            <button 
              onClick={() => {
                setCurrentPage(1);
                fetchVouchers();
              }}
              className="btn-primary"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      {/* Vouchers Table */}
      <div className="card">
        <div className="card-body p-0">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="spinner-lg" />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Code
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expires
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {vouchers.map((voucher) => (
                    <tr key={voucher.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm font-mono font-medium text-gray-900">
                            {voucher.code}
                          </span>
                          <button
                            onClick={() => copyVoucherCode(voucher.code)}
                            className="ml-2 text-gray-400 hover:text-gray-600"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {voucher.email ? (
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {voucher.email}
                            </div>
                            <div className="text-sm text-gray-500">
                              @{voucher.telegram_id}
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">No user</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {voucher.order_id ? (
                          <div>
                            <div className="text-sm font-mono text-gray-900">
                              {voucher.order_id.slice(0, 8)}...
                            </div>
                            {voucher.amount && (
                              <div className="text-sm text-gray-500">
                                {voucher.amount} {voucher.currency}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">Bulk created</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(voucher.status)}`}>
                          {getStatusIcon(voucher.status)}
                          <span className="ml-1 capitalize">{voucher.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(voucher.expires_at)}
                        </div>
                        {isExpired(voucher.expires_at) && voucher.status === 'active' && (
                          <div className="text-xs text-error-600">Expired</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(voucher.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        {voucher.redeemed_at && (
                          <div className="text-xs text-gray-500">
                            Redeemed: {formatDate(voucher.redeemed_at)}
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="btn-outline disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="btn-outline disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Bulk Create Modal */}
      {showBulkModal && (
        <BulkCreateModal
          onClose={() => setShowBulkModal(false)}
          onCreate={handleBulkCreate}
        />
      )}
    </div>
  );
}

// Bulk Create Modal Component
function BulkCreateModal({ 
  onClose, 
  onCreate 
}: { 
  onClose: () => void; 
  onCreate: (count: number, expiryDays: number) => void; 
}) {
  const [count, setCount] = useState(10);
  const [expiryDays, setExpiryDays] = useState(365);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onCreate(count, expiryDays);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={onClose} />
        <div className="relative bg-white rounded-lg max-w-md w-full p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Bulk Create Vouchers</h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Number of vouchers (1-1000)
              </label>
              <input
                type="number"
                min="1"
                max="1000"
                value={count}
                onChange={(e) => setCount(parseInt(e.target.value))}
                className="input-field"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expiry days (1-3650)
              </label>
              <input
                type="number"
                min="1"
                max="3650"
                value={expiryDays}
                onChange={(e) => setExpiryDays(parseInt(e.target.value))}
                className="input-field"
                required
              />
            </div>

            <div className="text-sm text-gray-600">
              <p>Vouchers will expire on: {new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
              >
                Create {count} Vouchers
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
