#!/usr/bin/env ts-node

/**
 * Secure Admin Setup Script
 * 
 * This script creates the initial admin user using environment variables
 * instead of hardcoded credentials. It's designed to be:
 * - Secure: No hardcoded passwords
 * - Environment-aware: Different configs for dev/prod
 * - Idempotent: Safe to run multiple times
 * - Production-ready: Validates strong passwords in production
 */

import dotenv from 'dotenv';
import bcrypt from 'bcrypt';
import { executeQuery } from '../config/database';
import { logger } from '../config/logger';

// Load environment variables
dotenv.config();

interface AdminConfig {
  email: string;
  telegramId: string;
  password: string;
  setupRequired: boolean;
}

/**
 * Get admin configuration from environment variables
 */
const getAdminConfig = (): AdminConfig => {
  const config: AdminConfig = {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    telegramId: process.env.ADMIN_TELEGRAM_ID || 'admin_dev',
    password: process.env.ADMIN_PASSWORD || 'DevAdmin123!',
    setupRequired: process.env.ADMIN_SETUP_REQUIRED === 'true',
  };

  return config;
};

/**
 * Validate admin configuration based on environment
 */
const validateAdminConfig = (config: AdminConfig): void => {
  const isProduction = process.env.NODE_ENV === 'production';

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(config.email)) {
    throw new Error('Invalid admin email format');
  }

  // Telegram ID validation
  if (!config.telegramId || config.telegramId.length < 3) {
    throw new Error('Invalid admin Telegram ID');
  }

  // Password validation - stricter in production
  if (isProduction) {
    if (config.password.length < 12) {
      throw new Error('Production admin password must be at least 12 characters');
    }
    
    const hasUpper = /[A-Z]/.test(config.password);
    const hasLower = /[a-z]/.test(config.password);
    const hasNumber = /\d/.test(config.password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(config.password);
    
    if (!hasUpper || !hasLower || !hasNumber || !hasSpecial) {
      throw new Error('Production admin password must contain uppercase, lowercase, number, and special character');
    }

    // Check for common weak passwords
    const weakPasswords = ['Admin123!', 'Password123!', 'admin123!'];
    if (weakPasswords.includes(config.password)) {
      throw new Error('Cannot use common weak passwords in production');
    }
  } else {
    // Development - more lenient but still secure
    if (config.password.length < 8) {
      throw new Error('Admin password must be at least 8 characters');
    }
  }
};

/**
 * Check if admin user already exists
 */
const checkAdminExists = async (email: string): Promise<boolean> => {
  try {
    const result = await executeQuery(
      'SELECT id FROM users WHERE email = $1 AND role = $2',
      [email, 'admin']
    );
    return result.rows.length > 0;
  } catch (error) {
    logger.error('Error checking admin existence:', error);
    throw error;
  }
};

/**
 * Create admin user in database
 */
const createAdminUser = async (config: AdminConfig): Promise<void> => {
  try {
    // Hash password with appropriate rounds for environment
    const saltRounds = process.env.NODE_ENV === 'production' ? 14 : 12;
    const passwordHash = await bcrypt.hash(config.password, saltRounds);

    // Insert admin user
    const result = await executeQuery(
      `INSERT INTO users (email, telegram_id, password_hash, email_verified, role, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING id, email`,
      [config.email, config.telegramId, passwordHash, true, 'admin']
    );

    if (result.rows.length > 0) {
      logger.info('Admin user created successfully:', {
        id: result.rows[0].id,
        email: result.rows[0].email,
        environment: process.env.NODE_ENV || 'development',
      });
    }
  } catch (error) {
    logger.error('Error creating admin user:', error);
    throw error;
  }
};

/**
 * Update existing admin user password
 */
const updateAdminPassword = async (email: string, password: string): Promise<void> => {
  try {
    const saltRounds = process.env.NODE_ENV === 'production' ? 14 : 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    await executeQuery(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2 AND role = $3',
      [passwordHash, email, 'admin']
    );

    logger.info('Admin password updated successfully:', {
      email,
      environment: process.env.NODE_ENV || 'development',
    });
  } catch (error) {
    logger.error('Error updating admin password:', error);
    throw error;
  }
};

/**
 * Main setup function
 */
const setupAdmin = async (): Promise<void> => {
  try {
    logger.info('Starting admin setup process...');

    // Get and validate configuration
    const config = getAdminConfig();
    validateAdminConfig(config);

    // Check if admin already exists
    const adminExists = await checkAdminExists(config.email);

    if (adminExists) {
      if (config.setupRequired) {
        logger.info('Admin user exists, updating password...');
        await updateAdminPassword(config.email, config.password);
      } else {
        logger.info('Admin user already exists, skipping setup');
      }
    } else {
      logger.info('Creating new admin user...');
      await createAdminUser(config);
    }

    logger.info('Admin setup completed successfully');
  } catch (error) {
    logger.error('Admin setup failed:', error);
    process.exit(1);
  }
};

// Run setup if called directly
if (require.main === module) {
  setupAdmin()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Setup failed:', error);
      process.exit(1);
    });
}

export { setupAdmin, getAdminConfig, validateAdminConfig };
