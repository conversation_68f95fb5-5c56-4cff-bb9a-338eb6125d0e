import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logAdminAction } from '../config/logger';
import { generateVoucherCode, generatePaginationMeta } from '../utils/helpers';

// Get admin dashboard statistics
export const getAdminStats = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;

    const statsResult = await executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE role IS NOT NULL) as total_users,
        (SELECT COUNT(*) FROM users WHERE email_verified = true) as verified_users,
        (SELECT COUNT(*) FROM orders) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'completed') as completed_orders,
        (SELECT COUNT(*) FROM vouchers) as total_vouchers,
        (SELECT COUNT(*) FROM vouchers WHERE status = 'active') as active_vouchers,
        (SELECT COALESCE(SUM(amount), 0) FROM orders WHERE status = 'completed') as total_revenue,
        (SELECT COUNT(*) FROM orders WHERE created_at >= CURRENT_DATE) as orders_today,
        (SELECT COUNT(*) FROM users WHERE created_at >= CURRENT_DATE) as users_today
    `);

    const stats = statsResult.rows[0];

    // Get recent activity
    const recentOrdersResult = await executeQuery(`
      SELECT o.id, o.amount, o.currency, o.status, o.created_at, u.telegram_id
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 10
    `);

    const recentUsersResult = await executeQuery(`
      SELECT id, email, telegram_id, email_verified, role, created_at
      FROM users
      WHERE role IS NOT NULL
      ORDER BY created_at DESC
      LIMIT 10
    `);

    logAdminAction('VIEW_DASHBOARD', adminId, 'dashboard', 'stats', {}, req);

    res.json({
      success: true,
      data: {
        stats: {
          totalUsers: parseInt(stats.total_users),
          verifiedUsers: parseInt(stats.verified_users),
          totalOrders: parseInt(stats.total_orders),
          completedOrders: parseInt(stats.completed_orders),
          totalVouchers: parseInt(stats.total_vouchers),
          activeVouchers: parseInt(stats.active_vouchers),
          totalRevenue: parseFloat(stats.total_revenue),
          ordersToday: parseInt(stats.orders_today),
          usersToday: parseInt(stats.users_today),
        },
        recentOrders: recentOrdersResult.rows,
        recentUsers: recentUsersResult.rows,
      },
    });
  } catch (error) {
    logger.error('Get admin stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get admin statistics',
    });
  }
};

// Get all users with pagination and filtering
export const getUsers = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const search = req.query.search as string;
    const role = req.query.role as string;
    const verified = req.query.verified as string;

    // Build query conditions
    let whereClause = 'WHERE role IS NOT NULL';
    const queryParams: any[] = [];

    if (search) {
      whereClause += ' AND (email ILIKE $1 OR telegram_id ILIKE $1)';
      queryParams.push(`%${search}%`);
    }

    if (role) {
      whereClause += ` AND role = $${queryParams.length + 1}`;
      queryParams.push(role);
    }

    if (verified) {
      whereClause += ` AND email_verified = $${queryParams.length + 1}`;
      queryParams.push(verified === 'true');
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM users ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get users
    const usersQuery = `
      SELECT id, email, telegram_id, email_verified, role, 
             failed_login_attempts, locked_until, last_login, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const usersResult = await executeQuery(usersQuery, queryParams);

    const pagination = generatePaginationMeta(total, page, limit);

    logAdminAction('VIEW_USERS', adminId, 'users', 'list', { search, role, verified }, req);

    res.json({
      success: true,
      data: {
        users: usersResult.rows,
        pagination,
      },
    });
  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get users',
    });
  }
};

// Update user role or status
export const updateUser = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { id } = req.params;
    const { role, emailVerified, locked } = req.body;

    // Get current user data
    const userResult = await executeQuery(
      'SELECT id, email, role, email_verified, locked_until FROM users WHERE id = $1',
      [id]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    // Prevent admin from changing their own role
    if (user.id === adminId && role && role !== user.role) {
      return res.status(400).json({
        success: false,
        error: 'Cannot change your own role',
      });
    }

    // Build update query
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (role !== undefined) {
      updates.push(`role = $${paramIndex++}`);
      params.push(role);
    }

    if (emailVerified !== undefined) {
      updates.push(`email_verified = $${paramIndex++}`);
      params.push(emailVerified);
    }

    if (locked !== undefined) {
      if (locked) {
        updates.push(`locked_until = $${paramIndex++}`);
        params.push(new Date(Date.now() + 24 * 60 * 60 * 1000)); // 24 hours
      } else {
        updates.push(`locked_until = NULL, failed_login_attempts = 0`);
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid updates provided',
      });
    }

    updates.push(`updated_at = CURRENT_TIMESTAMP`);
    params.push(id);

    const updateQuery = `
      UPDATE users 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, email, telegram_id, email_verified, role, locked_until, updated_at
    `;

    const updateResult = await executeQuery(updateQuery, params);
    const updatedUser = updateResult.rows[0];

    logAdminAction('UPDATE_USER', adminId, 'user', id, { 
      changes: { role, emailVerified, locked },
      userEmail: user.email,
    }, req);

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        user: updatedUser,
      },
    });
  } catch (error) {
    logger.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user',
    });
  }
};

// Get all orders with pagination and filtering
export const getOrders = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;
    const userId = req.query.userId as string;

    // Build query conditions
    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];

    if (status) {
      whereClause += ` AND o.status = $${queryParams.length + 1}`;
      queryParams.push(status);
    }

    if (userId) {
      whereClause += ` AND o.user_id = $${queryParams.length + 1}`;
      queryParams.push(userId);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM orders o ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get orders
    const ordersQuery = `
      SELECT o.id, o.user_id, o.status, o.amount, o.currency, o.memo,
             o.payment_address, o.transaction_hash, o.created_at, o.updated_at,
             u.email, u.telegram_id
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ${whereClause}
      ORDER BY o.created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const ordersResult = await executeQuery(ordersQuery, queryParams);

    const pagination = generatePaginationMeta(total, page, limit);

    logAdminAction('VIEW_ORDERS', adminId, 'orders', 'list', { status, userId }, req);

    res.json({
      success: true,
      data: {
        orders: ordersResult.rows,
        pagination,
      },
    });
  } catch (error) {
    logger.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get orders',
    });
  }
};

// Get all vouchers with pagination and filtering
export const getVouchers = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;
    const userId = req.query.userId as string;

    // Build query conditions
    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];

    if (status) {
      whereClause += ` AND v.status = $${queryParams.length + 1}`;
      queryParams.push(status);
    }

    if (userId) {
      whereClause += ` AND o.user_id = $${queryParams.length + 1}`;
      queryParams.push(userId);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) 
      FROM vouchers v 
      JOIN orders o ON v.order_id = o.id 
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get vouchers
    const vouchersQuery = `
      SELECT v.id, v.code, v.status, v.redeemed_at, v.expires_at, v.created_at,
             o.id as order_id, o.user_id, o.amount, o.currency,
             u.email, u.telegram_id
      FROM vouchers v
      JOIN orders o ON v.order_id = o.id
      JOIN users u ON o.user_id = u.id
      ${whereClause}
      ORDER BY v.created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const vouchersResult = await executeQuery(vouchersQuery, queryParams);

    const pagination = generatePaginationMeta(total, page, limit);

    logAdminAction('VIEW_VOUCHERS', adminId, 'vouchers', 'list', { status, userId }, req);

    res.json({
      success: true,
      data: {
        vouchers: vouchersResult.rows,
        pagination,
      },
    });
  } catch (error) {
    logger.error('Get vouchers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vouchers',
    });
  }
};

// Bulk create vouchers
export const bulkCreateVouchers = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { count, expiryDays = 365 } = req.body;

    if (!count || count < 1 || count > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Count must be between 1 and 1000',
      });
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiryDays);

    const vouchers = [];
    const queries = [];

    // Generate unique voucher codes
    for (let i = 0; i < count; i++) {
      let voucherCode: string;
      let isUnique = false;
      let attempts = 0;

      do {
        voucherCode = generateVoucherCode();
        const codeCheck = await executeQuery(
          'SELECT id FROM vouchers WHERE code = $1',
          [voucherCode]
        );
        isUnique = codeCheck.rows.length === 0;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        return res.status(500).json({
          success: false,
          error: 'Failed to generate unique voucher codes',
        });
      }

      const voucherId = uuidv4();
      vouchers.push({
        id: voucherId,
        code: voucherCode,
        expiresAt,
      });

      queries.push({
        text: `INSERT INTO vouchers (id, code, status, expires_at) VALUES ($1, $2, $3, $4)`,
        params: [voucherId, voucherCode, 'active', expiresAt],
      });
    }

    // Execute all inserts in a transaction
    await executeTransaction(queries);

    logAdminAction('BULK_CREATE_VOUCHERS', adminId, 'vouchers', 'bulk', { 
      count, 
      expiryDays,
      voucherIds: vouchers.map(v => v.id),
    }, req);

    res.status(201).json({
      success: true,
      message: `${count} vouchers created successfully`,
      data: {
        vouchers: vouchers.map(v => ({
          id: v.id,
          code: v.code,
          status: 'active',
          expiresAt: v.expiresAt,
        })),
      },
    });
  } catch (error) {
    logger.error('Bulk create vouchers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create vouchers',
    });
  }
};
