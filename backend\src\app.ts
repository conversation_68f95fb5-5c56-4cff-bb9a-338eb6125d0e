import express from 'express';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import { logger, morganStream } from './config/logger';
import {
  corsOptions,
  helmetConfig,
  generalRateLimit,
  speedLimiter,
  securityHeaders,
  detectSuspiciousActivity,
  checkBlockedIP,
  honeypot,
  requestSizeLimit,
} from './middleware/security';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/user';
import orderRoutes from './routes/orders';
import paymentRoutes from './routes/payments';
import voucherRoutes from './routes/vouchers';
import adminRoutes from './routes/admin';
import tonRoutes from './routes/ton';
import webhookRoutes from './routes/webhooks';

const app = express();

// Trust proxy (important for rate limiting and IP detection)
app.set('trust proxy', 1);

// Basic middleware
app.use(compression());
app.use(cookieParser());

// Security middleware (order is important)
app.use(helmetConfig);
app.use(cors(corsOptions));
app.use(securityHeaders);
app.use(checkBlockedIP);
app.use(detectSuspiciousActivity);
app.use(requestSizeLimit(10 * 1024 * 1024)); // 10MB limit

// Rate limiting
app.use(generalRateLimit);
app.use(speedLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Honeypot middleware
app.use(honeypot);

// HTTP request logging
if (process.env.NODE_ENV !== 'test') {
  const morgan = require('morgan');
  app.use(morgan('combined', { stream: morganStream }));
}

// Health check endpoint (before authentication)
app.get('/health', (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API routes
app.use('/api/v1', (_req, res, next) => {
  // Add API version header
  res.setHeader('API-Version', '1.0.0');
  next();
});

// Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/user', userRoutes);
app.use('/api/v1/orders', orderRoutes);
app.use('/api/v1/payments', paymentRoutes);
app.use('/api/v1/vouchers', voucherRoutes);
app.use('/api/v1/admin', adminRoutes);
app.use('/api/v1/ton', tonRoutes);
app.use('/api/v1/webhooks', webhookRoutes);

// Temporary test endpoint
app.get('/api/v1/test', (_req, res) => {
  res.json({
    success: true,
    message: 'API is working',
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use('*', (req, res) => {
  logger.warn(`404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    error: 'Route not found',
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(error.statusCode || 500).json({
    success: false,
    error: isDevelopment ? error.message : 'Internal server error',
    ...(isDevelopment && { stack: error.stack }),
  });
});

// Graceful shutdown handler
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}, shutting down gracefully...`);
  
  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Promise Rejection:', { reason, promise });
});

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

export default app;
