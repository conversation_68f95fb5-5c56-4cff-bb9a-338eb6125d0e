import nodemailer from 'nodemailer';
import { logger } from '../config/logger';
import { executeQuery } from '../config/database';

// Email template interface
interface EmailTemplate {
  to: string;
  subject: string;
  template: string;
  data: any;
}

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.EMAIL_SMTP_HOST,
    port: parseInt(process.env.EMAIL_SMTP_PORT || '587'),
    secure: process.env.EMAIL_SMTP_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

// Email templates
const templates = {
  'email-verification': {
    subject: 'Verify Your Email Address - TON Voucher Platform',
    html: (data: any) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #0ea5e9; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>TON Voucher Platform</h1>
            <p>Verify Your Email Address</p>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Thank you for registering with TON Voucher Platform. To complete your registration, please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${data.verificationLink}" class="button">Verify Email Address</a>
            </p>
            <p>This verification link will expire in ${data.expiresIn}.</p>
            <p>If you didn't create an account with us, please ignore this email.</p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #0ea5e9;">${data.verificationLink}</p>
          </div>
          <div class="footer">
            <p>© 2024 TON Voucher Platform. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data: any) => `
      Hello ${data.userName}!
      
      Thank you for registering with TON Voucher Platform. To complete your registration, please verify your email address by visiting this link:
      
      ${data.verificationLink}
      
      This verification link will expire in ${data.expiresIn}.
      
      If you didn't create an account with us, please ignore this email.
      
      © 2024 TON Voucher Platform. All rights reserved.
    `,
  },

  'password-reset': {
    subject: 'Reset Your Password - TON Voucher Platform',
    html: (data: any) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #ef4444; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>TON Voucher Platform</h1>
            <p>Password Reset Request</p>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>We received a request to reset your password for your TON Voucher Platform account.</p>
            <p style="text-align: center;">
              <a href="${data.resetLink}" class="button">Reset Password</a>
            </p>
            <div class="warning">
              <strong>Security Notice:</strong> This password reset link will expire in ${data.expiresIn}. If you didn't request this reset, please ignore this email and your password will remain unchanged.
            </div>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #ef4444;">${data.resetLink}</p>
          </div>
          <div class="footer">
            <p>© 2024 TON Voucher Platform. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data: any) => `
      Hello ${data.userName}!
      
      We received a request to reset your password for your TON Voucher Platform account.
      
      To reset your password, visit this link:
      ${data.resetLink}
      
      This password reset link will expire in ${data.expiresIn}.
      
      If you didn't request this reset, please ignore this email and your password will remain unchanged.
      
      © 2024 TON Voucher Platform. All rights reserved.
    `,
  },

  'voucher-delivery': {
    subject: 'Your Voucher Code is Ready! - TON Voucher Platform',
    html: (data: any) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Voucher Delivery</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #22c55e, #16a34a); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .voucher-code { background: #1e293b; color: #f1f5f9; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0; font-family: monospace; font-size: 24px; letter-spacing: 2px; }
          .order-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Payment Confirmed!</h1>
            <p>Your voucher code is ready</p>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Great news! Your payment has been confirmed and your voucher code is ready for use.</p>

            <div class="voucher-code">
              <strong>${data.voucherCode}</strong>
            </div>

            <div class="order-details">
              <h3>Order Details</h3>
              <p><strong>Order ID:</strong> ${data.orderDetails.orderId}</p>
              <p><strong>Amount:</strong> ${data.orderDetails.amount} ${data.orderDetails.currency}</p>
              <p><strong>Date:</strong> ${new Date(data.orderDetails.createdAt).toLocaleDateString()}</p>
            </div>

            <p><strong>Important:</strong> Please save this voucher code in a secure location. You can also access it anytime from your account dashboard.</p>
            <p>This voucher code is valid for 365 days from the date of purchase.</p>
          </div>
          <div class="footer">
            <p>© 2024 TON Voucher Platform. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data: any) => `
      Hello ${data.userName}!

      Great news! Your payment has been confirmed and your voucher code is ready for use.

      Your Voucher Code: ${data.voucherCode}

      Order Details:
      - Order ID: ${data.orderDetails.orderId}
      - Amount: ${data.orderDetails.amount} ${data.orderDetails.currency}
      - Date: ${new Date(data.orderDetails.createdAt).toLocaleDateString()}

      Important: Please save this voucher code in a secure location. You can also access it anytime from your account dashboard.

      This voucher code is valid for 365 days from the date of purchase.

      © 2024 TON Voucher Platform. All rights reserved.
    `,
  },

  'welcome': {
    subject: 'Welcome to TON Voucher Platform!',
    html: (data: any) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #0ea5e9; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .features { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to TON Voucher Platform!</h1>
            <p>Your account is now verified and ready to use</p>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Congratulations! Your email has been verified and your account is now active. You can start purchasing premium voucher codes with TON cryptocurrency.</p>

            <div class="features">
              <h3>What you can do now:</h3>
              <ul>
                <li>✅ Browse and purchase premium voucher codes</li>
                <li>✅ Pay securely with TON cryptocurrency</li>
                <li>✅ Receive instant voucher delivery</li>
                <li>✅ Track your order history</li>
                <li>✅ Access 24/7 customer support</li>
              </ul>
            </div>

            <p style="text-align: center;">
              <a href="${data.dashboardLink}" class="button">Go to Dashboard</a>
            </p>

            <p>If you have any questions, feel free to contact our support team. We're here to help!</p>
          </div>
          <div class="footer">
            <p>© 2024 TON Voucher Platform. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data: any) => `
      Welcome to TON Voucher Platform!

      Hello ${data.userName}!

      Congratulations! Your email has been verified and your account is now active. You can start purchasing premium voucher codes with TON cryptocurrency.

      What you can do now:
      - Browse and purchase premium voucher codes
      - Pay securely with TON cryptocurrency
      - Receive instant voucher delivery
      - Track your order history
      - Access 24/7 customer support

      Visit your dashboard: ${data.dashboardLink}

      If you have any questions, feel free to contact our support team. We're here to help!

      © 2024 TON Voucher Platform. All rights reserved.
    `,
  },

  'order-confirmation': {
    subject: 'Order Confirmation - TON Voucher Platform',
    html: (data: any) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .order-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
          .payment-info { background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; border: 1px solid #fbbf24; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📋 Order Confirmation</h1>
            <p>Your order has been created</p>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Thank you for your order! We've received your request and are waiting for payment confirmation.</p>

            <div class="order-details">
              <h3>Order Details</h3>
              <p><strong>Order ID:</strong> ${data.orderDetails.orderId}</p>
              <p><strong>Amount:</strong> ${data.orderDetails.amount} ${data.orderDetails.currency}</p>
              <p><strong>Status:</strong> ${data.orderDetails.status}</p>
              <p><strong>Created:</strong> ${new Date(data.orderDetails.createdAt).toLocaleDateString()}</p>
            </div>

            <div class="payment-info">
              <h3>⏰ Payment Required</h3>
              <p>Please complete your payment to receive your voucher code. Your order will expire in ${data.expiresIn} if payment is not received.</p>
              <p><strong>Payment Address:</strong> ${data.paymentAddress}</p>
            </div>

            <p>Once payment is confirmed, you'll receive your voucher code via email and it will be available in your dashboard.</p>
          </div>
          <div class="footer">
            <p>© 2024 TON Voucher Platform. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data: any) => `
      Order Confirmation - TON Voucher Platform

      Hello ${data.userName}!

      Thank you for your order! We've received your request and are waiting for payment confirmation.

      Order Details:
      - Order ID: ${data.orderDetails.orderId}
      - Amount: ${data.orderDetails.amount} ${data.orderDetails.currency}
      - Status: ${data.orderDetails.status}
      - Created: ${new Date(data.orderDetails.createdAt).toLocaleDateString()}

      Payment Required:
      Please complete your payment to receive your voucher code. Your order will expire in ${data.expiresIn} if payment is not received.

      Payment Address: ${data.paymentAddress}

      Once payment is confirmed, you'll receive your voucher code via email and it will be available in your dashboard.

      © 2024 TON Voucher Platform. All rights reserved.
    `,
  },
};

// Send email function
export const sendEmail = async (emailData: EmailTemplate): Promise<void> => {
  try {
    const transporter = createTransporter();
    const template = templates[emailData.template as keyof typeof templates];
    
    if (!template) {
      throw new Error(`Email template '${emailData.template}' not found`);
    }

    const mailOptions = {
      from: `${process.env.EMAIL_FROM_NAME || 'TON Voucher Platform'} <${process.env.EMAIL_FROM_ADDRESS || process.env.EMAIL_USER}>`,
      to: emailData.to,
      subject: emailData.subject || template.subject,
      html: template.html(emailData.data),
      text: template.text(emailData.data),
    };

    const result = await transporter.sendMail(mailOptions);
    
    // Log email sent
    await logEmailSent(emailData.to, emailData.template, emailData.subject || template.subject, 'sent', result.messageId);
    
    logger.info('Email sent successfully', {
      to: emailData.to,
      template: emailData.template,
      messageId: result.messageId,
    });
  } catch (error) {
    // Log email failed
    await logEmailSent(emailData.to, emailData.template, emailData.subject || 'Unknown', 'failed', undefined, (error as Error).message);
    
    logger.error('Failed to send email', {
      to: emailData.to,
      template: emailData.template,
      error: (error as Error).message,
    });
    
    throw error;
  }
};

// Log email to database
const logEmailSent = async (
  recipientEmail: string,
  emailType: string,
  subject: string,
  status: string,
  externalId?: string,
  errorMessage?: string
): Promise<void> => {
  try {
    await executeQuery(
      `INSERT INTO email_logs (recipient_email, email_type, subject, status, external_id, error_message)
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [recipientEmail, emailType, subject, status, externalId, errorMessage]
    );
  } catch (error) {
    logger.error('Failed to log email to database', error);
  }
};

// Test email configuration
export const testEmailConfiguration = async (): Promise<boolean> => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    logger.info('Email configuration is valid');
    return true;
  } catch (error) {
    logger.error('Email configuration test failed', error);
    return false;
  }
};
