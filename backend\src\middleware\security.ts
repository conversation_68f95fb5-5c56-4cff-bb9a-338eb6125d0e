import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { body, validationResult, ValidationChain } from 'express-validator';
import { logger, logSecurityEvent } from '../config/logger';
import { redis } from '../config/database';

// CORS configuration
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logSecurityEvent('CORS_VIOLATION', { origin, allowedOrigins });
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With'],
  exposedHeaders: ['X-CSRF-Token'],
};

// Helmet security configuration
export const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

// Rate limiting configurations
export const createRateLimit = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: message || 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      logSecurityEvent('RATE_LIMIT_EXCEEDED', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
      }, req);
      
      res.status(429).json({
        success: false,
        error: 'Too many requests, please try again later.',
      });
    },
    skip: (req: Request) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/api/health';
    },
  });
};

// General rate limiting
export const generalRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests per window
  'Too many requests from this IP, please try again later.'
);

// Authentication rate limiting
export const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts per window
  'Too many authentication attempts, please try again later.'
);

// Order creation rate limiting
export const orderRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 orders per window
  'Too many order creation attempts, please try again later.'
);

// Slow down middleware for repeated requests
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: () => 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
});

// Input validation middleware
export const validateInput = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logSecurityEvent('VALIDATION_FAILED', {
        errors: errors.array(),
        body: req.body,
        path: req.path,
      }, req);
      
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      });
    }
    
    next();
  };
};

// Common validation rules
export const emailValidation = body('email')
  .isEmail()
  .normalizeEmail()
  .isLength({ max: 255 })
  .withMessage('Valid email is required');

export const passwordValidation = body('password')
  .isLength({ min: 8, max: 128 })
  .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .withMessage('Password must be 8-128 characters with uppercase, lowercase, number, and special character');

export const telegramIdValidation = body('telegramId')
  .matches(/^[a-zA-Z0-9_]{5,32}$/)
  .withMessage('Telegram ID must be 5-32 characters, alphanumeric and underscores only');

export const amountValidation = body('amount')
  .isFloat({ min: 0.01, max: 1000000 })
  .withMessage('Amount must be between 0.01 and 1,000,000');

export const memoValidation = body('memo')
  .optional()
  .isLength({ max: 500 })
  .trim()
  .escape()
  .withMessage('Memo must be less than 500 characters');

// IP whitelist middleware
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    const clientIP = req.ip || req.connection.remoteAddress || '';

    if (!allowedIPs.includes(clientIP)) {
      logSecurityEvent('IP_NOT_WHITELISTED', { clientIP, allowedIPs }, req);
      return res.status(403).json({
        success: false,
        error: 'Access denied',
      });
    }
    
    next();
  };
};

// Request size limiting
export const requestSizeLimit = (maxSize: number) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    const contentLength = parseInt(req.get('content-length') || '0');
    
    if (contentLength > maxSize) {
      logSecurityEvent('REQUEST_SIZE_EXCEEDED', {
        contentLength,
        maxSize,
        path: req.path,
      }, req);
      
      return res.status(413).json({
        success: false,
        error: 'Request entity too large',
      });
    }
    
    next();
  };
};

// Security headers middleware
export const securityHeaders = (_req: Request, res: Response, next: NextFunction): void => {
  // Remove server information
  res.removeHeader('X-Powered-By');

  // Add custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  next();
};

// Suspicious activity detection
export const detectSuspiciousActivity = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  const clientIP = req.ip;
  const userAgent = req.get('User-Agent') || '';
  const path = req.path;
  
  // Check for common attack patterns
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /eval\(/i,  // Code injection
  ];
  
  const requestData = JSON.stringify(req.body) + req.url + req.get('User-Agent');
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestData)) {
      logSecurityEvent('SUSPICIOUS_ACTIVITY_DETECTED', {
        pattern: pattern.toString(),
        path,
        method: req.method,
        userAgent,
        body: req.body,
      }, req);
      
      // Temporarily block this IP
      await redis.setEx(`blocked_ip:${clientIP}`, 3600, 'suspicious_activity'); // 1 hour block
      
      return res.status(403).json({
        success: false,
        error: 'Suspicious activity detected',
      });
    }
  }
  
  next();
};

// Check if IP is blocked
export const checkBlockedIP = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  const clientIP = req.ip;
  
  try {
    const isBlocked = await redis.get(`blocked_ip:${clientIP}`);
    
    if (isBlocked) {
      logSecurityEvent('BLOCKED_IP_ACCESS_ATTEMPT', { clientIP }, req);
      return res.status(403).json({
        success: false,
        error: 'Access temporarily restricted',
      });
    }
  } catch (error) {
    logger.error('Error checking blocked IP:', error);
  }
  
  next();
};

// Honeypot middleware (trap for bots)
export const honeypot = (req: Request, res: Response, next: NextFunction): void | Response => {
  // Check for honeypot field in forms
  if (req.body && req.body.website) {
    logSecurityEvent('HONEYPOT_TRIGGERED', {
      honeypotValue: req.body.website,
      path: req.path,
    }, req);
    
    // Silently reject the request
    return res.status(200).json({
      success: true,
      message: 'Request processed',
    });
  }
  
  next();
};
