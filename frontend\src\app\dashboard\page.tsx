'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ShoppingCart,
  Gift,
  CreditCard,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
} from 'lucide-react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useTonConnect } from '@/contexts/TonConnectContext';
import WalletConnection from '@/components/wallet/WalletConnection';

interface DashboardStats {
  totalOrders: number;
  activeVouchers: number;
  totalSpent: string;
  pendingOrders: number;
}

interface RecentOrder {
  id: string;
  amount: string;
  currency: string;
  status: string;
  createdAt: string;
}

interface RecentVoucher {
  id: string;
  code: string;
  status: string;
  createdAt: string;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    activeVouchers: 0,
    totalSpent: '0.00',
    pendingOrders: 0,
  });
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [recentVouchers, setRecentVouchers] = useState<RecentVoucher[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // TODO: Replace with actual API calls
      // Simulated data for now
      setStats({
        totalOrders: 12,
        activeVouchers: 8,
        totalSpent: '245.50',
        pendingOrders: 1,
      });

      setRecentOrders([
        {
          id: 'ORD-001',
          amount: '25.00',
          currency: 'TON',
          status: 'completed',
          createdAt: '2024-01-15T10:30:00Z',
        },
        {
          id: 'ORD-002',
          amount: '50.00',
          currency: 'TON',
          status: 'pending',
          createdAt: '2024-01-14T15:45:00Z',
        },
      ]);

      setRecentVouchers([
        {
          id: 'VOUCH-001',
          code: 'GAME1234ABCD',
          status: 'active',
          createdAt: '2024-01-15T10:35:00Z',
        },
        {
          id: 'VOUCH-002',
          code: 'VIP5678EFGH',
          status: 'redeemed',
          createdAt: '2024-01-14T16:00:00Z',
        },
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'active':
        return 'text-success-600 bg-success-100';
      case 'pending':
        return 'text-warning-600 bg-warning-100';
      case 'failed':
      case 'cancelled':
        return 'text-error-600 bg-error-100';
      case 'redeemed':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="spinner-lg mx-auto mb-4" />
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-primary rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome back, {user?.telegramId}!
          </h1>
          <p className="text-ton-100">
            Manage your vouchers and track your orders from your dashboard.
          </p>
          {!user?.emailVerified && (
            <div className="mt-4 p-3 bg-warning-500 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">
                  Please verify your email address to access all features.
                </span>
                <Link
                  href="/auth/verify-email"
                  className="ml-2 text-sm underline hover:no-underline"
                >
                  Verify now
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* Wallet Connection */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">TON Wallet</h2>
                <p className="text-sm text-gray-600">Connect your wallet to make payments</p>
              </div>
              <div className="card-body">
                <WalletConnection />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
            </div>
            <div className="card-body space-y-3">
              <Link
                href="/dashboard/orders/new"
                className="btn-primary w-full inline-flex items-center justify-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Order
              </Link>
              <Link
                href="/dashboard/vouchers"
                className="btn-outline w-full inline-flex items-center justify-center"
              >
                <Gift className="h-4 w-4 mr-2" />
                View Vouchers
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ShoppingCart className="h-8 w-8 text-ton-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Orders</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Gift className="h-8 w-8 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Active Vouchers</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeVouchers}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCard className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Spent</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalSpent} TON</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Pending Orders</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pendingOrders}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Link
                href="/shop"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ton-300 hover:shadow-md transition-all"
              >
                <Plus className="h-6 w-6 text-ton-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Purchase Voucher</p>
                  <p className="text-sm text-gray-500">Buy premium game vouchers</p>
                </div>
              </Link>

              <Link
                href="/dashboard/orders"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ton-300 hover:shadow-md transition-all"
              >
                <ShoppingCart className="h-6 w-6 text-ton-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">View Orders</p>
                  <p className="text-sm text-gray-500">Track your order history</p>
                </div>
              </Link>

              <Link
                href="/dashboard/vouchers"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ton-300 hover:shadow-md transition-all"
              >
                <Gift className="h-6 w-6 text-ton-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">My Vouchers</p>
                  <p className="text-sm text-gray-500">Access your voucher codes</p>
                </div>
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">Recent Orders</h2>
                <Link
                  href="/dashboard/orders"
                  className="text-sm text-ton-600 hover:text-ton-500"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="card-body">
              {recentOrders.length > 0 ? (
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{order.id}</p>
                        <p className="text-sm text-gray-500">
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {order.amount} {order.currency}
                        </p>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No orders yet</p>
              )}
            </div>
          </div>

          {/* Recent Vouchers */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">Recent Vouchers</h2>
                <Link
                  href="/dashboard/vouchers"
                  className="text-sm text-ton-600 hover:text-ton-500"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="card-body">
              {recentVouchers.length > 0 ? (
                <div className="space-y-4">
                  {recentVouchers.map((voucher) => (
                    <div key={voucher.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-mono text-sm font-medium text-gray-900">
                          {voucher.code}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(voucher.createdAt)}
                        </p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(voucher.status)}`}>
                        {voucher.status}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No vouchers yet</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
