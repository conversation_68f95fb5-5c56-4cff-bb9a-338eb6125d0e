import { Request, Response } from 'express';
import { executeQuery } from '../config/database';
import { logger, logSecurityEvent } from '../config/logger';
import { hashPassword } from '../middleware/auth';
import { sanitizeString } from '../utils/helpers';

// Get user profile
export const getProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const userResult = await executeQuery(
      `SELECT id, email, telegram_id, email_verified, role, created_at, updated_at, last_login
       FROM users WHERE id = $1`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          telegramId: user.telegram_id,
          emailVerified: user.email_verified,
          role: user.role,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
          lastLogin: user.last_login,
        },
      },
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile',
    });
  }
};

// Update user profile
export const updateProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { telegramId, memo } = req.body;

    // Sanitize inputs
    const sanitizedTelegramId = sanitizeString(telegramId);
    const sanitizedMemo = memo ? sanitizeString(memo) : null;

    // Check if telegram ID is already taken by another user
    if (sanitizedTelegramId) {
      const existingUser = await executeQuery(
        'SELECT id FROM users WHERE telegram_id = $1 AND id != $2',
        [sanitizedTelegramId, userId]
      );

      if (existingUser.rows.length > 0) {
        return res.status(409).json({
          success: false,
          error: 'Telegram ID is already taken',
        });
      }
    }

    // Update user profile
    const updateResult = await executeQuery(
      `UPDATE users 
       SET telegram_id = COALESCE($1, telegram_id),
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $2
       RETURNING id, email, telegram_id, email_verified, role, created_at, updated_at`,
      [sanitizedTelegramId, userId]
    );

    if (updateResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const updatedUser = updateResult.rows[0];

    logger.info('User profile updated', { userId, changes: { telegramId: sanitizedTelegramId } });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          telegramId: updatedUser.telegram_id,
          emailVerified: updatedUser.email_verified,
          role: updatedUser.role,
          createdAt: updatedUser.created_at,
          updatedAt: updatedUser.updated_at,
        },
      },
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile',
    });
  }
};

// Change password
export const changePassword = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { currentPassword, newPassword } = req.body;

    // Get current password hash
    const userResult = await executeQuery(
      'SELECT password_hash FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    // Verify current password
    const bcrypt = require('bcrypt');
    const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
    
    if (!isValidPassword) {
      logSecurityEvent('INVALID_PASSWORD_CHANGE_ATTEMPT', { userId }, req);
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect',
      });
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update password
    await executeQuery(
      `UPDATE users 
       SET password_hash = $1, 
           updated_at = CURRENT_TIMESTAMP,
           failed_login_attempts = 0,
           locked_until = NULL
       WHERE id = $2`,
      [newPasswordHash, userId]
    );

    // Invalidate all existing sessions for this user
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE user_id = $1',
      [userId]
    );

    logSecurityEvent('PASSWORD_CHANGED', { userId }, req);

    res.json({
      success: true,
      message: 'Password changed successfully. Please log in again.',
    });
  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to change password',
    });
  }
};

// Get user orders
export const getUserOrders = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await executeQuery(
      'SELECT COUNT(*) FROM orders WHERE user_id = $1',
      [userId]
    );
    const total = parseInt(countResult.rows[0].count);

    // Get orders
    const ordersResult = await executeQuery(
      `SELECT id, status, amount, currency, memo, payment_address, 
              transaction_hash, created_at, updated_at
       FROM orders 
       WHERE user_id = $1 
       ORDER BY created_at DESC 
       LIMIT $2 OFFSET $3`,
      [userId, limit, offset]
    );

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        orders: ordersResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get user orders error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get orders',
    });
  }
};

// Get user vouchers
export const getUserVouchers = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;

    // Build query conditions
    let whereClause = 'WHERE o.user_id = $1';
    const queryParams: any[] = [userId];

    if (status) {
      whereClause += ' AND v.status = $2';
      queryParams.push(status);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) 
      FROM vouchers v 
      JOIN orders o ON v.order_id = o.id 
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get vouchers
    const vouchersQuery = `
      SELECT v.id, v.code, v.status, v.redeemed_at, v.expires_at, v.created_at,
             o.id as order_id, o.amount, o.currency
      FROM vouchers v 
      JOIN orders o ON v.order_id = o.id 
      ${whereClause}
      ORDER BY v.created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const vouchersResult = await executeQuery(vouchersQuery, queryParams);

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        vouchers: vouchersResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get user vouchers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vouchers',
    });
  }
};

// Delete user account
export const deleteAccount = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { password, confirmDelete } = req.body;

    if (confirmDelete !== 'DELETE') {
      return res.status(400).json({
        success: false,
        error: 'Please confirm account deletion by typing "DELETE"',
      });
    }

    // Verify password
    const userResult = await executeQuery(
      'SELECT password_hash FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];
    const bcrypt = require('bcrypt');
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    
    if (!isValidPassword) {
      logSecurityEvent('INVALID_ACCOUNT_DELETION_ATTEMPT', { userId }, req);
      return res.status(400).json({
        success: false,
        error: 'Password is incorrect',
      });
    }

    // Check for active orders or vouchers
    const activeItemsResult = await executeQuery(
      `SELECT 
         (SELECT COUNT(*) FROM orders WHERE user_id = $1 AND status IN ('pending', 'payment_pending', 'paid')) as active_orders,
         (SELECT COUNT(*) FROM vouchers v JOIN orders o ON v.order_id = o.id WHERE o.user_id = $1 AND v.status = 'active') as active_vouchers`,
      [userId]
    );

    const { active_orders, active_vouchers } = activeItemsResult.rows[0];

    if (active_orders > 0 || active_vouchers > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete account with active orders or vouchers. Please contact support.',
      });
    }

    // Soft delete user (mark as deleted instead of actually deleting)
    await executeQuery(
      `UPDATE users 
       SET email = CONCAT('deleted_', id, '@deleted.local'),
           telegram_id = CONCAT('deleted_', id),
           password_hash = 'deleted',
           email_verified = false,
           email_verification_token = NULL,
           password_reset_token = NULL,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $1`,
      [userId]
    );

    // Invalidate all sessions
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE user_id = $1',
      [userId]
    );

    logSecurityEvent('ACCOUNT_DELETED', { userId }, req);

    res.json({
      success: true,
      message: 'Account deleted successfully',
    });
  } catch (error) {
    logger.error('Delete account error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete account',
    });
  }
};
