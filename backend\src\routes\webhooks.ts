import { Router } from 'express';
import {
  handleTonPaymentWebhook,
  verifyTransactionManually,
  getWebhookLogs,
} from '../controllers/webhookController';
import {
  validateInput,
} from '../middleware/security';
import { authenticate, requireAdmin } from '../middleware/auth';
import { body, query } from 'express-validator';

const router = Router();

// TON payment webhook validation
const tonPaymentWebhookValidation = [
  body('transactionHash')
    .matches(/^[a-fA-F0-9]{64}$/)
    .withMessage('Invalid transaction hash format'),
  body('fromAddress')
    .notEmpty()
    .withMessage('From address is required'),
  body('toAddress')
    .notEmpty()
    .withMessage('To address is required'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Invalid amount'),
  body('memo')
    .optional()
    .isString()
    .withMessage('Memo must be a string'),
  body('timestamp')
    .optional()
    .isISO8601()
    .withMessage('Invalid timestamp format'),
];

// Manual verification validation
const manualVerificationValidation = [
  body('transactionHash')
    .matches(/^[a-fA-F0-9]{64}$/)
    .withMessage('Invalid transaction hash format'),
  body('orderId')
    .isUUID()
    .withMessage('Invalid order ID format'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

/**
 * @route   POST /api/v1/webhooks/ton-payment
 * @desc    Handle TON payment webhook notifications
 * @access  Public (but should be secured with webhook signature in production)
 */
router.post(
  '/ton-payment',
  validateInput(tonPaymentWebhookValidation),
  handleTonPaymentWebhook
);

/**
 * @route   POST /api/v1/webhooks/verify-transaction
 * @desc    Manually verify a transaction (admin only)
 * @access  Private (Admin)
 */
router.post(
  '/verify-transaction',
  authenticate,
  requireAdmin,
  validateInput(manualVerificationValidation),
  verifyTransactionManually
);

/**
 * @route   GET /api/v1/webhooks/logs
 * @desc    Get webhook processing logs (admin only)
 * @access  Private (Admin)
 */
router.get(
  '/logs',
  authenticate,
  requireAdmin,
  validateInput(paginationValidation),
  getWebhookLogs
);

export default router;
